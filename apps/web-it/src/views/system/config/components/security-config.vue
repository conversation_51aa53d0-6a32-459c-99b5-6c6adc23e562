<script setup lang="ts">
import type { Ref } from 'vue';

import type { ConfigInfo } from '@vben/types';

import { inject, ref } from 'vue';

import {
  Button,
  Checkbox,
  Col,
  Form,
  FormItem,
  Input,
  InputNumber,
  Row,
  Select,
  SelectOption,
  Switch,
  TabPane,
  Tabs,
  Textarea,
} from 'ant-design-vue';

import {
  configInfoInjectionKey,
  configLoadingInjectionKey,
  configSaveInjectionKey,
} from '#/views/system/config/config-injection-key';

const configForm = inject(configInfoInjectionKey) as Ref<Partial<ConfigInfo>>;
const saveConfig = inject(configSaveInjectionKey) as Function;
const loading = inject(configLoadingInjectionKey) as Ref<{ save: boolean }>;
const activeKey = ref('login');
</script>

<template>
  <Tabs v-model:active-key="activeKey" tab-position="left">
    <TabPane key="login" tab="登录策略">
      <Row>
        <Col :span="12">
          <div class="m-4">
            <Form :model="configForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" :colon="false">
              <!-- 登录方式 -->
              <FormItem label="登录方式" name="loginType">
                <Select v-model:value="configForm.loginType">
                  <SelectOption value="1">单一登录</SelectOption>
                  <SelectOption value="2">同时登录</SelectOption>
                </Select>
              </FormItem>
              <!-- 登录超时 -->
              <FormItem label="登录超时" name="tokenTimeout">
                <InputNumber
                  v-model:value="configForm.tokenTimeout"
                  addon-after="分钟"
                  :controls="false"
                  :precision="0"
                  :min="0"
                  class="w-full"
                />
              </FormItem>
              <!-- 错误锁定时间 -->
              <FormItem label="错误锁定时间" name="lockTime">
                <InputNumber
                  v-model:value="configForm.lockTime"
                  addon-after="分钟"
                  :controls="false"
                  :precision="0"
                  :min="0"
                  class="w-full"
                />
              </FormItem>
              <!-- 密码错误次数 -->
              <FormItem label="密码错误次数" name="passwordErrorsNumber">
                <InputNumber
                  v-model:value="configForm.passwordErrorsNumber"
                  addon-after="次"
                  :controls="false"
                  :precision="0"
                  :min="0"
                  class="w-full"
                />
              </FormItem>
              <!-- 登录验证码 -->
              <FormItem label="登录验证码" name="verificationCodeSwitch">
                <Switch v-model:checked="configForm.verificationCodeSwitch" :checked-value="1" :un-checked-value="0" />
              </FormItem>
              <!-- 验证码类型 -->
              <FormItem label="验证码类型" name="verificationCodeType" v-if="configForm.verificationCodeSwitch">
                <Select v-model:value="configForm.verificationCodeType">
                  <SelectOption value="slider">滑动验证码</SelectOption>
                  <SelectOption value="pic">图片验证码</SelectOption>
                </Select>
              </FormItem>
              <!-- 白名单验证 -->
              <FormItem label="白名单验证" name="whitelistSwitch">
                <Switch v-model:checked="configForm.whitelistSwitch" :checked-value="1" :un-checked-value="0" />
              </FormItem>
              <!-- 白名单设置 -->
              <FormItem v-if="configForm.whitelistSwitch" label="白名单设置" name="whitelistIp">
                <Textarea
                  v-model:value="configForm.whitelistIp"
                  placeholder="多个IP设置，用英文符号隔开，如***********,***********"
                />
              </FormItem>
              <!-- 登录安全提示 -->
              <FormItem label="登录安全提示" name="loginMsgSwitch">
                <Switch v-model:checked="configForm.loginMsgSwitch" :checked-value="1" :un-checked-value="0" />
              </FormItem>
              <FormItem class="text-right" :wrapper-col="{ span: 24 }">
                <Button type="primary" :loading="loading.save" @click="saveConfig(configForm as ConfigInfo)">
                  保存
                </Button>
              </FormItem>
            </Form>
          </div>
        </Col>
      </Row>
    </TabPane>
    <TabPane key="password" tab="密码策略">
      <Row>
        <Col :span="12">
          <div class="m-4">
            <Form :model="configForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" :colon="false">
              <!-- 密码定期更新 -->
              <FormItem label="密码定期更新" name="passwordIsUpdatedRegularly">
                <Switch
                  v-model:checked="configForm.passwordIsUpdatedRegularly"
                  :checked-value="1"
                  :un-checked-value="0"
                />
              </FormItem>
              <template v-if="configForm.passwordIsUpdatedRegularly">
                <!-- 更新周期 -->
                <FormItem label="更新周期" name="passwordUpdatedCycle">
                  <InputNumber
                    v-model:value="configForm.passwordUpdatedCycle"
                    addon-after="天"
                    :controls="false"
                    :precision="0"
                    :min="0"
                    class="w-full"
                  />
                </FormItem>
                <!-- 提前 -->
                <FormItem label="提前" name="reminderDaysInAdvance">
                  <InputNumber
                    v-model:value="configForm.reminderDaysInAdvance"
                    addon-after="天提醒更新"
                    :controls="false"
                    :precision="0"
                    :min="0"
                    class="w-full"
                  />
                </FormItem>
              </template>
              <!-- 密码强度限制 -->
              <FormItem label="密码强度限制" name="passwordStrengthLimit">
                <Switch v-model:checked="configForm.passwordStrengthLimit" :checked-value="1" :un-checked-value="0" />
              </FormItem>
              <template v-if="configForm.passwordStrengthLimit">
                <!-- 最小长度 -->
                <FormItem label="" name="passwordLengthMin" :wrapper-col="{ span: 18, offset: 6 }">
                  <Checkbox v-model:value="configForm.passwordLengthMin">最小长度</Checkbox>
                </FormItem>
                <!-- 最小长度的数值 -->
                <FormItem
                  v-if="configForm.passwordLengthMin"
                  label=""
                  name="passwordLengthMinNumber"
                  :wrapper-col="{ span: 18, offset: 6 }"
                >
                  <InputNumber
                    v-model:value="configForm.passwordLengthMinNumber"
                    addon-after="位"
                    :controls="false"
                    :precision="0"
                    :min="0"
                  />
                </FormItem>
                <!-- 包含数字 -->
                <FormItem label="" name="containsNumbers" :wrapper-col="{ span: 18, offset: 6 }">
                  <Checkbox v-model:value="configForm.containsNumbers">包含数字</Checkbox>
                </FormItem>
                <!-- 包含小写字母 -->
                <FormItem label="" name="includeLowercaseLetters" :wrapper-col="{ span: 18, offset: 6 }">
                  <Checkbox v-model:value="configForm.includeLowercaseLetters">包含小写字母</Checkbox>
                </FormItem>
                <!-- 包含大写字母 -->
                <FormItem label="" name="includeUppercaseLetters" :wrapper-col="{ span: 18, offset: 6 }">
                  <Checkbox v-model:value="configForm.includeUppercaseLetters">包含大写字母</Checkbox>
                </FormItem>
                <!-- 包含符号 -->
                <FormItem label="" name="containsCharacters" :wrapper-col="{ span: 18, offset: 6 }">
                  <Checkbox v-model:value="configForm.containsCharacters">包含符号</Checkbox>
                </FormItem>
              </template>
              <!-- 禁用旧密码 -->
              <FormItem label="禁用旧密码" name="disableOldPassword">
                <Switch v-model:checked="configForm.disableOldPassword" :checked-value="1" :un-checked-value="0" />
              </FormItem>
              <!-- 禁用个数 -->
              <FormItem v-if="configForm.disableOldPassword" label="禁用个数" name="disableNumberOfOldPasswords">
                <InputNumber
                  v-model:value="configForm.disableNumberOfOldPasswords"
                  addon-after="个"
                  :controls="false"
                  :precision="0"
                  :min="0"
                />
              </FormItem>
              <!-- 修改初始密码提醒 -->
              <FormItem label="修改初始密码提醒" name="mandatoryModificationOfInitialPassword">
                <Switch
                  v-model:checked="configForm.mandatoryModificationOfInitialPassword"
                  :checked-value="1"
                  :un-checked-value="0"
                />
              </FormItem>
              <!-- 默认密码 -->
              <FormItem label="默认密码" name="newUserDefaultPassword">
                <Input v-model:value="configForm.newUserDefaultPassword" />
              </FormItem>
              <FormItem class="text-right" :wrapper-col="{ span: 24 }">
                <Button type="primary" :loading="loading.save" @click="saveConfig(configForm as ConfigInfo)">
                  保存
                </Button>
              </FormItem>
            </Form>
          </div>
        </Col>
      </Row>
    </TabPane>
  </Tabs>
</template>

<style></style>
