<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { MessageInfo, NoticeInfo } from '@vben/types';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { message, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  allReadMessageApi,
  batchDeleteMessageApi,
  batchReadMessageApi,
  getMyMessageDetailApi,
  getMyMessagePageListApi,
} from '#/api';
import { useDictStore } from '#/store/dict';

const emit = defineEmits(['updateStatus']);
const dictStore = useDictStore();
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'title',
      label: '标题',
    },
    {
      component: 'Select',
      fieldName: 'noticeType',
      label: '类型',
      componentProps: {
        options: dictStore.getDictList('NOTICE_TYPE'),
      },
    },
    {
      component: 'Select',
      fieldName: 'readFlag',
      label: '状态',
      componentProps: {
        options: dictStore.getDictList('NOTICE_READ_TYPE'),
      },
    },
  ],
});
const gridOptions: VxeTableGridOptions<NoticeInfo> = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { type: 'seq', title: '序号', width: 60 },
    { field: 'title', title: '标题', maxWidth: 180, slots: { default: 'title' } },
    {
      field: 'msgType',
      title: '类型',
      cellRender: { name: 'CellStatus', props: { code: 'MESSAGE_TYPE' } },
      maxWidth: 100,
    },
    {
      field: 'readFlag',
      title: '状态',
      cellRender: { name: 'CellStatus', props: { code: 'NOTICE_READ_TYPE' } },
      maxWidth: 100,
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getMyMessagePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [Modal, modalApi] = useVbenModal({
  footer: false,
  centered: true,
  onClosed() {
    emit('updateStatus');
    gridApi.reload();
  },
});
const messageDetail = ref<MessageInfo>({});
const view = async (row: NoticeInfo) => {
  if (row.id) {
    messageDetail.value = await getMyMessageDetailApi({ id: row.id });
    modalApi.setState({ title: messageDetail.value.title });
    modalApi.open();
  }
};
const allRead = async () => {
  await confirm('确认全部已读吗？', '确认全部已读');
  await allReadMessageApi();
  await gridApi.reload();
  emit('updateStatus');
};
const read = async () => {
  const rows = gridApi.grid.getCheckboxRecords();
  if (rows.length === 0) {
    return message.warning('请先选择一条数据');
  }
  await confirm('确认已读吗？', '确认已读');
  const ids = rows.map((row) => row.id!);
  await batchReadMessageApi(ids);
  await gridApi.reload();
  emit('updateStatus');
};
const del = async () => {
  const rows = gridApi.grid.getCheckboxRecords();
  if (rows.length === 0) {
    return message.warning('请先选择一条数据');
  }
  await confirm('确认删除吗？', '确认删除');
  const ids = rows.map((row) => row.id!);
  await batchDeleteMessageApi(ids);
  await gridApi.reload();
  emit('updateStatus');
};
</script>

<template>
  <div class="h-full">
    <Grid>
      <template #toolbar-actions>
        <a-button class="mr-2" type="primary" @click="allRead">全部已读</a-button>
        <a-button class="mr-2" type="primary" @click="read">已读</a-button>
        <a-button class="mr-2" type="primary" danger @click="del">删除</a-button>
      </template>
      <template #title="{ row }">
        <TypographyLink @click="view(row)">{{ row.title }}</TypographyLink>
      </template>
    </Grid>
    <Modal class="w-[800px]">
      <div class="flex h-full flex-col justify-between">
        <div class="mt-4 min-h-[80px] flex-1 bg-white">
          {{ messageDetail.content }}
        </div>
        <div class="mt-4 flex justify-between">
          <p class="text-sm text-gray-400">发送时间：{{ messageDetail.createTime }}</p>
          <TypographyLink v-if="messageDetail.urlPath" :href="messageDetail.urlPath" target="_blank" class="text-sm">
            消息链接
          </TypographyLink>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style></style>
