<script setup lang="ts">
import type { NoticeInfo } from '@vben/types';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { getMyNoticeDetailApi } from '#/api';

defineEmits(['register', 'ok']);
const noticeDetail = ref<NoticeInfo>({});
const init = async (data: NoticeInfo) => {
  if (data.id) {
    changeLoading(true);
    try {
      noticeDetail.value = await getMyNoticeDetailApi({ id: data.id });
    } finally {
      changeLoading(false);
    }
  }
};
const close = () => {
  noticeDetail.value = {};
};
const [registerPopup, { changeLoading }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="通知详情" height="100%" @register="registerPopup" @close="close">
    <div :class="BASE_PAGE_CLASS_NAME" class="flex h-full flex-col">
      <div>
        <h3 class="text-center text-lg font-bold">{{ noticeDetail.title }}</h3>
        <div class="mx-auto mt-4 flex w-[600px] justify-between">
          <p>发布人：{{ noticeDetail.createByName }}</p>
          <p>发布时间：{{ noticeDetail.createTime }}</p>
        </div>
      </div>
      <div class="mx-auto mt-4 min-h-[600px] w-[1000px] flex-1 border bg-white p-4">
        <div v-html="noticeDetail.content"></div>
      </div>
    </div>
  </BasicPopup>
</template>

<style></style>
