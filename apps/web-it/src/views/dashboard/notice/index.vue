<script setup lang="ts">
import { ref } from 'vue';

import { ColPage } from '@vben/common-ui';

import { Badge, Menu, MenuItem } from 'ant-design-vue';

import { getUnreadMessageCountApi, getUnReadNoticeCountApi } from '#/api';
import MessageList from '#/views/dashboard/notice/components/message-list.vue';
import NoticeList from '#/views/dashboard/notice/components/notice-list.vue';

const selectedKeys = ref(['message']);
const menuItems = ref([
  {
    key: 'message',
    label: '我的消息',
    count: 0,
  },
  {
    key: 'notice',
    label: '我的公告',
    count: 0,
  },
]);
const getUnReadCount = async () => {
  Promise.all([getUnreadMessageCountApi(), getUnReadNoticeCountApi()]).then((res) => {
    menuItems.value[0]!.count = res[0];
    menuItems.value[1]!.count = res[1];
  });
};
getUnReadCount();
</script>

<template>
  <ColPage :resizable="false" auto-content-height :left-width="20" :right-width="80">
    <template #left>
      <div class="mr-4 h-full bg-white">
        <Menu v-model:selected-keys="selectedKeys" mode="inline">
          <MenuItem v-for="item in menuItems" :key="item.key">
            <div class="flex items-center justify-between">
              <span>{{ item.label }}</span>
              <Badge :count="item.count" />
            </div>
          </MenuItem>
        </Menu>
      </div>
    </template>
    <MessageList v-if="selectedKeys.includes('message')" @update-status="getUnReadCount" />
    <NoticeList v-else-if="selectedKeys.includes('notice')" @update-status="getUnReadCount" />
  </ColPage>
</template>

<style></style>
