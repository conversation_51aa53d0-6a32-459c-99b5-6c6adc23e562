<script setup lang="ts">
import type { NoticeInfo } from '@vben/types';

import { computed, reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, FORM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';

import { BaseFeUserSelect } from '#/adapter/fe-ui';
import { getNoticeDetailApi, saveNoticeApi } from '#/api';
import Editor from '#/components/Editor.vue';
import { useDictStore } from '#/store/dict';

const emit = defineEmits(['register', 'ok']);
const dictStore = useDictStore();
const state = reactive({
  loading: false,
  formProp: FORM_PROP,
});
const noticeForm = ref<NoticeInfo>({
  noticeScope: '1',
  noticeMethods: ['1'],
  content: '',
});
const publishTime = computed({
  get() {
    return dayjs(noticeForm.value.publishTime).format('x');
  },
  set(value) {
    noticeForm.value.publishTime = Number(value);
  },
});
const init = async (data: NoticeInfo) => {
  if (data.id) {
    changeLoading(true);
    try {
      noticeForm.value = await getNoticeDetailApi({ id: data.id });
    } finally {
      changeLoading(false);
    }
  }
};
const FormRef = ref();
const save = async (status: 'DRAFTING' | 'PUBLISHED') => {
  await FormRef.value.validate();
  changeOkLoading(true);
  state.loading = true;
  try {
    const formData = cloneDeep(noticeForm.value);
    formData.status = status;
    await saveNoticeApi(noticeForm.value);
    emit('ok', noticeForm.value);
    closePopup();
  } finally {
    changeOkLoading(false);
    state.loading = false;
  }
};
const close = () => {
  noticeForm.value = {
    noticeScope: '1',
    noticeMethods: ['1'],
    content: '',
  };
};
const title = computed(() => {
  return noticeForm.value.id ? '编辑公告' : '新增公告';
});
const rules = {
  title: [{ required: true, message: '请输入标题' }],
  noticeType: [{ required: true, message: '请选择分类' }],
  noticeScope: [{ required: true, message: '请选择通知范围' }],
  publishTime: [{ required: true, message: '请选择发布时间' }],
  noticeUsers: [{ required: true, message: '请选择通知用户' }],
};
const [registerPopup, { closePopup, changeOkLoading, changeLoading }] = usePopupInner(init);
</script>

<template>
  <BasicPopup
    v-bind="$attrs"
    :title="title"
    show-ok-btn
    :ok-text="noticeForm.status === 'PUBLISHED' ? '保存' : '发布'"
    @register="registerPopup"
    @ok="save('PUBLISHED')"
    @close="close"
  >
    <template #insertToolbar>
      <a-space>
        <a-button
          v-if="noticeForm.status !== 'PUBLISHED'"
          type="primary"
          :loading="state.loading"
          ghost
          @click="save('DRAFTING')"
        >
          草稿
        </a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME" class="mt-4">
      <a-form ref="FormRef" :model="noticeForm" :rules="rules" v-bind="state.formProp">
        <a-row>
          <a-col :span="12">
            <a-form-item label="标题" name="title">
              <a-input v-model:value="noticeForm.title" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="分类" name="noticeType">
              <a-select v-model:value="noticeForm.noticeType" :options="dictStore.getDictList('NOTICE_TYPE')" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="通知范围" name="noticeScope">
              <a-radio-group
                v-model:value="noticeForm.noticeScope"
                :disabled="noticeForm.status === 'PUBLISHED'"
                :options="dictStore.getDictList('NOTICE_SCOPE')"
              />
            </a-form-item>
          </a-col>
          <a-col v-if="noticeForm.noticeScope?.includes('2')" :span="12">
            <a-form-item label="通知用户" name="noticeUsers">
              <BaseFeUserSelect
                v-model:value="noticeForm.noticeUsers"
                :disabled="noticeForm.status === 'PUBLISHED'"
                multiple
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="通知方式" name="noticeMethods">
              <a-checkbox-group
                v-model:value="noticeForm.noticeMethods"
                :disabled="noticeForm.status === 'PUBLISHED'"
                :options="dictStore.getDictList('NOTICE_METHOD')"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="定时发布" name="isTiming">
              <a-switch
                v-model:checked="noticeForm.isTiming"
                :checked-value="1"
                :un-checked-value="0"
                :disabled="noticeForm.status === 'PUBLISHED'"
              />
            </a-form-item>
          </a-col>
          <a-col v-if="noticeForm.isTiming" :span="12">
            <a-form-item label="发布时间" name="publishTime">
              <a-date-picker
                v-model:value="publishTime"
                value-format="x"
                :disabled="noticeForm.status === 'PUBLISHED'"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="置顶" name="isTop">
              <a-switch v-model:checked="noticeForm.isTop" :checked-value="1" :un-checked-value="0" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <div class="h-[500px]">
              <Editor v-model="noticeForm.content" />
            </div>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
