<script setup lang="ts">
import type { NoticeInfo } from '@vben/types';

import { ref } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { getNoticeDetailApi } from '#/api';

defineEmits(['register', 'ok']);
const noticeForm = ref<NoticeInfo>({});
const init = async (data: NoticeInfo) => {
  if (data.id) {
    changeLoading(true);
    try {
      noticeForm.value = await getNoticeDetailApi({ id: data.id });
    } finally {
      changeLoading(false);
    }
  }
};
const close = () => {
  noticeForm.value = {};
};
const [registerPopup, { changeLoading }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="通知详情" @register="registerPopup" @close="close">
    <div :class="BASE_PAGE_CLASS_NAME" class="mt-4">
      <a-descriptions v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="标题">
          {{ noticeForm.title }}
        </a-descriptions-item>
        <a-descriptions-item label="分类">
          <StatusTag code="NOTICE_TYPE" :value="noticeForm.noticeType" />
        </a-descriptions-item>
        <a-descriptions-item label="通知范围">
          <StatusTag code="NOTICE_SCOPE" :value="noticeForm.noticeScope" />
        </a-descriptions-item>
        <a-descriptions-item label="通知方式">
          <StatusTag v-for="item in noticeForm.noticeMethods" :key="item" code="NOTICE_METHOD" :value="item" />
        </a-descriptions-item>
        <a-descriptions-item label="发布时间">
          {{ noticeForm.publishTime }}
        </a-descriptions-item>
        <a-descriptions-item label="置顶">
          <StatusTag code="baseBooleanType" :value="noticeForm.isTop" />
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <StatusTag code="NOTICE_STATUS" :value="noticeForm.status" />
        </a-descriptions-item>
        <a-descriptions-item label="创建人">
          {{ noticeForm.createByName }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="公告内容" class="mb-8" />
      <div class="mx-auto min-h-[300px] w-[1000px] border bg-white p-4">
        <div v-html="noticeForm.content"></div>
      </div>
    </div>
  </BasicPopup>
</template>

<style></style>
