<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';
import type { NoticeInfo } from '@vben/types';

import type { LogLoginInfo } from '#/api';

import { StatusTag } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getNoticePageListApi } from '#/api';
import { useDictStore } from '#/store/dict';
import DetailPopup from '#/views/notice/manage/detail-popup.vue';
import EditPopup from '#/views/notice/manage/edit-popup.vue';

const dictStore = useDictStore();
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'title',
      label: '标题',
    },
    {
      component: 'Select',
      fieldName: 'noticeType',
      label: '分类',
      componentProps: {
        options: dictStore.getDictList('NOTICE_TYPE'),
      },
    },
  ],
});
const gridOptions: VxeTableGridOptions<NoticeInfo> = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { type: 'seq', title: '序号', width: 60 },
    { field: 'title', title: '公告标题', maxWidth: 180 },
    { field: 'createByName', title: '发布人', maxWidth: 120 },
    {
      field: 'noticeScope',
      title: '通知范围',
      cellRender: { name: 'CellStatus', props: { code: 'NOTICE_SCOPE' } },
      width: 110,
    },
    {
      field: 'noticeMethods',
      title: '通知方式',
      slots: { default: 'noticeMethods' },
      maxWidth: 165,
    },
    {
      field: 'noticeType',
      title: '分类',
      cellRender: { name: 'CellStatus', props: { code: 'NOTICE_TYPE' } },
      maxWidth: 100,
    },
    {
      field: 'status',
      title: '状态',
      cellRender: { name: 'CellStatus', props: { code: 'NOTICE_STATUS' } },
      maxWidth: 100,
    },
    {
      field: 'isTiming',
      title: '是否定时',
      cellRender: { name: 'CellStatus', props: { code: 'baseBooleanType' } },
      width: 110,
    },
    { field: 'publishTime', title: '发布时间', width: 180 },
    {
      field: 'isTop',
      title: '是否置顶',
      cellRender: { name: 'CellStatus', props: { code: 'baseBooleanType' } },
      maxWidth: 100,
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 100,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getNoticePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [registerEdit, { openPopup: openEditPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const edit = (row: LogLoginInfo) => {
  openEditPopup(true, row);
};
const add = () => {
  openEditPopup(true, {});
};
const editSuccess = () => {
  gridApi.reload();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="add">新增</a-button>
        </a-space>
      </template>
      <template #noticeMethods="{ row }">
        <StatusTag v-for="item in row.noticeMethods" :key="item" :value="item" code="NOTICE_METHOD" class="mr-2" />
      </template>
      <template #action="{ row }">
        <a-space>
          <TypographyLink @click="openDetailPopup(true, row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
        </a-space>
      </template>
    </Grid>
    <EditPopup @register="registerEdit" @ok="editSuccess" />
    <DetailPopup @register="registerDetail" />
  </Page>
</template>

<style></style>
