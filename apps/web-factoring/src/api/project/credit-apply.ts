import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface CreditApplyInfo {
  /**
   * 用信申请人编码
   */
  applyCompanyCode?: string;
  /**
   * 用信申请人名称
   */
  applyCompanyName?: string;
  calculation?: ProjectCreditApplyCalculationBO;
  contract?: ProjectCreditApplyContractBO;
  /**
   * 用信申请报告文件ID
   */
  creditApplicationFileId?: number;
  /**
   * 授信批复额度（元）
   */
  creditApprovalAmount?: number;
  /**
   * 授信批复时间
   */
  creditApprovalDate?: Date;
  /**
   * 授信企业编码
   */
  creditCompanyCode?: string;
  /**
   * 授信企业名称
   */
  creditCompanyName?: string;
  /**
   * 授信费率（%/年）
   */
  creditRate?: number;
  /**
   * 授信期限（个月）
   */
  creditTerm?: number;
  /**
   * 支持保理方向
   */
  factoringDirection?: string;
  /**
   * 保理类型
   */
  factoringType?: string;
  /**
   * 资金用途说明
   */
  fundUsageDesc?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 项目已投放金额（元）
   */
  investedAmount?: number;
  /**
   * 是否为省重点产业
   */
  isProvincialKeyIndustry?: boolean;
  /**
   * 是否审批
   */
  isReview?: number;
  /**
   * 提交操作
   */
  isSubmit?: boolean;
  /**
   * 是否支持实体经济
   */
  isSupportRealEconomy?: boolean;
  /**
   * 运营复核报告文件ID
   */
  operationReviewFileId?: number;
  /**
   * 用信申请名称
   */
  projectCreditApplyName?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 项目定价ID
   */
  projectPricingId?: number;
  /**
   * 应收账款关系
   */
  receivableRefList?: ProjectCreditApplyReceivableRefBO[];
  /**
   * 追索权要求
   */
  recourseRequired?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 审批状态
   */
  reviewStatus?: string;
  /**
   * 风控复核报告文件ID
   */
  riskReviewFileId?: number;
  /**
   * 操作状态
   */
  status?: string;
  /**
   * 项目存量余额（元）
   */
  stockBalance?: number;
  /**
   * 支持操作模式
   */
  supportMode?: string;
  /**
   * 授信企业编码
   */
  targetCompanyCode?: string;
  /**
   * 授信企业名称
   */
  targetCompanyName?: string;
  /**
   * 投放行业
   */
  targetIndustry?: string;
  [property: string]: any;
}

/**
 * ProjectCreditApplyCalculationBO，项目用信申请试算
 */
export interface ProjectCreditApplyCalculationBO {
  /**
   * 用信申请金额（元）
   */
  applyAmount?: number;
  /**
   * 用信申请人编码
   */
  applyCompanyCode?: string;
  /**
   * 用信申请人名称
   */
  applyCompanyName?: string;
  /**
   * 增信措施说明
   */
  creditEnhancementDesc?: string;
  /**
   * 试算明细
   */
  detailList?: ProjectCreditApplyCalculationDetailBO[];
  /**
   * 提用方式
   */
  drawingMethod?: string;
  /**
   * 预估最后还款日
   */
  expectedDueDate?: Date;
  /**
   * 预估计息天数（天）
   */
  expectedInterestDays?: number;
  /**
   * 预计业务投放日
   */
  expectedLaunchDate?: Date;
  /**
   * 预估还款期数
   */
  expectedRepayPeriods?: number;
  /**
   * 融资比例（%）
   */
  financingRatio?: number;
  /**
   * 第一还款来源
   */
  firstRepaySource?: string;
  /**
   * 宽限期天数（天）
   */
  gracePeriodDays?: number;
  /**
   * 宽限期费率（%/年）
   */
  gracePeriodRate?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 分期还息频次
   */
  interestPeriod?: string;
  /**
   * 还息方式
   */
  interestRepaymentMethod?: string;
  /**
   * 放款主体编码
   */
  lenderCompanyCode?: string;
  /**
   * 放款主体名称
   */
  lenderCompanyName?: string;
  /**
   * 合同利率（%/年）
   */
  nominalInterestRate?: number;
  /**
   * 固定罚息利率（%）
   */
  penaltyInterestRate?: number;
  /**
   * 阶梯罚息规则（JSON格式）
   */
  penaltySteps?: string;
  /**
   * 罚息类型
   */
  penaltyType?: string;
  /**
   * 还本付息计划规划方式
   */
  planningMethod?: string;
  /**
   * 用信定价综合收益率（%）
   */
  pricingXirrRate?: number;
  /**
   * 分期还本频次
   */
  principalPeriod?: string;
  /**
   * 还本方式
   */
  principalRepaymentMethod?: string;
  /**
   * 项目用信申请ID
   */
  projectCreditApplyId?: number;
  /**
   * 综合收益率（%）
   */
  projectXirrRate?: number;
  /**
   * 应收账款金额（元）
   */
  receivableAmount?: number;
  /**
   * 默认当期还息日
   */
  repayInterestDay?: string;
  /**
   * 默认当期还本日
   */
  repayPrincipalDay?: string;
  /**
   * 服务费（元）
   */
  serviceFeeAmount?: number;
  /**
   * 补充还款来源
   */
  supplementRepaySource?: string;
  /**
   * 测算综合收益率（%/年）
   */
  xirrRate?: number;
  [property: string]: any;
}

/**
 * ProjectCreditApplyCalculationDetailBO，项目用信申请试算明细
 */
export interface ProjectCreditApplyCalculationDetailBO {
  /**
   * 当期还本/付息日
   */
  currentDate?: Date;
  /**
   * 应还宽限期利息(元)
   */
  graceInterestAmount?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 应还利息(元)
   */
  interestAmount?: number;
  /**
   * 应还逾期罚息(元)
   */
  overdueInterestAmount?: number;
  /**
   * 应还本金(元)
   */
  principalAmount?: number;
  /**
   * 试算ID
   */
  projectCreditApplyCalculationId?: number;
  /**
   * 还款项
   */
  repaymentItem?: string;
  /**
   * 还款期数
   */
  repayPeriods?: number;
  /**
   * 应收服务费(元)
   */
  serviceAmount?: number;
  /**
   * 当期净现金流(元)
   */
  totalAmount?: number;
  [property: string]: any;
}

/**
 * ProjectCreditApplyContractBO，项目用信申请合同
 */
export interface ProjectCreditApplyContractBO {
  /**
   * 原合同金额（元）
   */
  contractAmount?: number;
  /**
   * 底层资产描述
   */
  contractAssets?: string;
  /**
   * 原合同编号
   */
  contractCode?: string;
  /**
   * 债权人名称
   */
  contractCreditor?: string;
  /**
   * 债权人编码
   */
  contractCreditorCode?: string;
  /**
   * 原合同到期日期
   */
  contractDueDate?: Date;
  /**
   * 保理商名称
   */
  contractFactor?: string;
  /**
   * 保理商编码
   */
  contractFactorCode?: string;
  /**
   * 承租人名称
   */
  contractLessee?: string;
  /**
   * 承租人编码
   */
  contractLesseeCode?: string;
  /**
   * 出租人名称
   */
  contractLessor?: string;
  /**
   * 出租人编码
   */
  contractLessorCode?: string;
  /**
   * 原合同已回收本金（元）
   */
  contractRecycleAmount?: number;
  /**
   * 剩余权益价值金额（元）
   */
  contractRemainingAmount?: number;
  /**
   * 原合同签署日期
   */
  contractSignDate?: Date;
  /**
   * 拟转让应收账款概况
   */
  contractTransferredDesc?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 项目用信申请ID
   */
  projectCreditApplyId?: number;
  [property: string]: any;
}

/**
 * ProjectCreditApplyReceivableRefBO，项目用信申请应收账款关系
 */
export interface ProjectCreditApplyReceivableRefBO {
  /**
   * 业务类型
   */
  bizType?: string;
  /**
   * 债权人、债务人关系
   */
  creditorDebtorDel?: string;
  /**
   * 债权人
   */
  creditorName?: string;
  /**
   * 债务人
   */
  debtorName?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 应收账款池编号
   */
  poolCode?: string;
  /**
   * 应收账款池到期日
   */
  poolDueDate?: Date;
  /**
   * 应收账款池名称
   */
  poolName?: string;
  /**
   * 池内资产总额
   */
  poolTotalAmount?: number;
  /**
   * 应收账款池有效期（月）
   */
  poolValidity?: number;
  /**
   * 项目用信申请ID
   */
  projectCreditApplyId?: number;
  /**
   * 应收账款金额
   */
  receivableAmount?: number;
  /**
   * 应收账款编号
   */
  receivableCode?: string;
  /**
   * 应收账款到期日
   */
  receivableDueDate?: Date;
  /**
   * 应收账款ID
   */
  receivableId?: number;
  /**
   * 应收账款名称
   */
  receivableName?: string;
  /**
   * 应收账款池ID
   */
  receivablePoolId?: number;
  /**
   * 应收账款期限(月)
   */
  receivableTerm?: number;
  [property: string]: any;
}

// 获取项目用信分页列表
export async function getCreditApplyPageListApi(params: PageListParams) {
  return requestClient.get<CreditApplyInfo[]>('/factoring/project/credit/apply/page', { params });
}

// 添加项目用信
export async function addCreditApplyApi(data: CreditApplyInfo) {
  return requestClient.post<CreditApplyInfo>('/factoring/project/credit/apply/add', data);
}

// 编辑项目用信
export async function editCreditApplyApi(data: CreditApplyInfo) {
  return requestClient.post<CreditApplyInfo>('/factoring/project/credit/apply/edit', data);
}

// 获取项目用信详情
export async function getCreditApplyInfoApi(id: number) {
  return requestClient.get(`/factoring/project/credit/apply/detail/${id}`);
}

// 删除项目用信
export async function delCreditApplyApi(id: number) {
  return requestClient.post(`/factoring/project/credit/apply/delete/${id}`);
}

// 试算
export async function calculationProjectApply(data: ProjectCreditApplyCalculationDetailBO) {
  return requestClient.post<ProjectCreditApplyCalculationDetailBO>('/factoring/project/credit/apply/start', data);
}

// 上传用信申请报告
export async function creditUploadApplication(data: CreditApplyInfo) {
  return requestClient.post('/factoring/project/credit/apply/upload/application', data);
}

// 上传运营复核报告
export async function creditUploadOperation(data: CreditApplyInfo) {
  return requestClient.post('/factoring/project/credit/apply/upload/operation', data);
}

// 上传风控复核报告
export async function creditUploadRisk(data: CreditApplyInfo) {
  return requestClient.post('/factoring/project/credit/apply/upload/risk', data);
}

// 试算
export async function calculationCreditApply(data: ProjectCreditApplyCalculationDetailBO) {
  return requestClient.post<ProjectCreditApplyCalculationDetailBO>('/factoring/project/credit/apply/start', data);
}

// 仅更新利息
export async function calculationCreditApplyInterest(data: ProjectCreditApplyCalculationDetailBO) {
  return requestClient.post<ProjectCreditApplyCalculationDetailBO>(
    '/factoring/project/credit/apply/start/interest',
    data,
  );
}

// 试算明细导出
export async function calculationCreditApplyApi(id: number) {
  return requestClient.downloadAndSave(`/factoring/project/credit/apply/export?id=${id}`);
}

// 获取项目申请列表
export async function getCreditApplyListApi(params: PageListParams) {
  return requestClient.get<CreditApplyInfo[]>('/factoring/project/credit/apply/list', { params });
}
