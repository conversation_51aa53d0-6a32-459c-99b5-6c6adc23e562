import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface RepaymentReminderInfo {
  /**
   * 还款单位银行账号
   */
  bankAccount?: string;
  /**
   * 还款单位开户行
   */
  bankBranch?: string;
  /**
   * 还款单位
   */
  companyName?: string;
  /**
   * 合同号
   */
  contractCode?: string;
  /**
   * 合同名称
   */
  contractName?: string;
  /**
   * 待支付日期
   */
  dueDate?: Date;
  /**
   * 到期天数
   */
  dueDays?: number;
  /**
   * 还款计划明细ID
   */
  id?: number;
  /**
   * 待还利息（元）
   */
  interestAmount?: number;
  /**
   * 通知函文件ID
   */
  notifyFileId?: number;
  /**
   * 发送状态
   */
  notifyStatus?: string;
  /**
   * 第N期
   */
  periods?: number;
  /**
   * 待还本金（元）
   */
  principalAmount?: number;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 还款计划编号
   */
  repaymentPlanCode?: string;
  /**
   * 还款计划ID
   */
  repaymentPlanId?: number;
  /**
   * 本息合计（元）
   */
  totalAmount?: number;
  [property: string]: any;
}

// 获取分页列表
export async function getRepaymentReminderListApi(params: PageListParams) {
  return requestClient.get<RepaymentReminderInfo[]>('/factoring/warn/repayment/page', { params });
}

// 生成结清证明
export async function getRepaymentReminderGenerateApi(id: number) {
  return requestClient.post(`/factoring/warn/repayment/send?id=${id}`);
}
