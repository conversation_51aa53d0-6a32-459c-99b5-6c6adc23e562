import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface CompanyQuotaInfo {
  /**
   * 决策日期
   */
  beginDate?: string;
  /**
   * 地级市
   */
  cityName?: string;
  /**
   * 累计用信额度（元）
   */
  companyAccumulatedUsedAmount?: string;
  /**
   * 可用额度（元）
   */
  companyAvailableAmount?: string;
  /**
   * 授信额度（元）
   */
  companyCreditAmount?: string;
  /**
   * 企业名称
   */
  companyName?: string;
  /**
   * 已结清额度（元）
   */
  companySettlementAmount?: string;
  /**
   * 存量业务余额（元）
   */
  companyStockBalanceAmount?: string;
  /**
   * 授信期限
   */
  creditTerm?: string;
  /**
   * 授信方式
   */
  creditType?: string;
  /**
   * 区县
   */
  districtName?: string;
  /**
   * 授信到期日
   */
  endDate?: string;
  [property: string]: any;
}

// 获取分页列表
export async function getCompanyQuotaListApi(params: PageListParams) {
  return requestClient.get<CompanyQuotaInfo[]>('/factoring/ledger/limit/company/page', { params });
}

// 导出
export async function exportLedgerCompanyApi(data: CompanyQuotaInfo) {
  return requestClient.downloadAndSave('/factoring/ledger/limit/company/export', {
    config: { params: data },
  });
}
