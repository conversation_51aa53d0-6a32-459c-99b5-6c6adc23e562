import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface OperationInfo {
  /**
   * 本金余额（元）
   */
  balanceAmount?: string;
  /**
   * 融资客户
   */
  companyName?: string;
  /**
   * 合同年利率（%）
   */
  contractRate?: string;
  /**
   * 应收利息（元）
   */
  interestAmount?: string;
  /**
   * 投放金额（元）
   */
  launchAmount?: string;
  /**
   * 投放日
   */
  launchDate?: string;
  /**
   * 起息日
   */
  planDate?: string;
  /**
   * 应收本金（元）
   */
  principalAmount?: string;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 应收服务费（元）
   */
  serviceAmount?: string;
  /**
   * 回款日
   */
  settlementDate?: string;
  /**
   * 应回款总额（元）
   */
  totalAmount?: string;
  [property: string]: any;
}

// 获取分页列表
export async function getOperationListApi(params: PageListParams) {
  return requestClient.get<OperationInfo[]>('/factoring/ledger/operation/page', { params });
}

// 导出
export async function exportLedgerOperationApi(data: OperationInfo) {
  return requestClient.downloadAndSave('/factoring/ledger/operation/export', {
    config: { params: data },
  });
}
