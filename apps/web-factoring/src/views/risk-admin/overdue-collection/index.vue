<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OverdueCollectionInfo } from '#/api';

import { computed, ref } from 'vue';

import { FilePreviewDialog } from '@vben/base-ui';
import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getDownloadFileLinkApi,
  getOverdueCollectionGenerateApi,
  getOverdueCollectionListApi,
  getPreviewFileExternalLink,
} from '#/api';

const formOptions = defineFormOptions({
  schema: [
    { component: 'Input', fieldName: 'projectName', label: '项目名称' },
    { component: 'Input', fieldName: 'companyName', label: '还款单位' },
    { component: 'Input', fieldName: 'contractName', label: '合同名称' },
    { component: 'Input', fieldName: 'contractCode', label: '合同号' },
  ],
  commonConfig: { labelCol: { span: 8 }, wrapperCol: { span: 16 } },
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    { field: 'companyName', title: '还款单位', minWidth: 180 },
    { field: 'contractSignDate', title: '合同签订日期', minWidth: 120, formatter: 'formatDate' },
    { field: 'contractName', title: '合同名称', minWidth: 200 },
    { field: 'contractCode', title: '合同号', minWidth: 160 },
    { field: 'periods', title: '期数', minWidth: 80 },
    { field: 'dueDate', title: '应支付日期', minWidth: 120, formatter: 'formatDate' },
    { field: 'totalAmount', title: '本息合计（元）', minWidth: 120 },
    { field: 'overdueDays', title: '逾期天数', minWidth: 80 },
    {
      field: 'overdueStatus',
      title: '发送状态',
      minWidth: 120,
      cellRender: { name: 'CellStatus', props: { code: 'FCT_REPAYMENT_NOTIFY_STATUS' } },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 140,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) =>
        await getOverdueCollectionListApi({ current: page.currentPage, size: page.pageSize, ...formValues }),
    },
  },
  toolbarConfig: { slots: { tools: 'toolbar-tools' } },
};

const [Grid, GridApi] = useVbenVxeGrid({ formOptions, gridOptions });

const [SettlementModal, settlementModalApi] = useVbenModal({
  title: '发送通知函',
  confirmText: '发送通知函',
  onConfirm: async () => {
    if (!collectionData.value.deadline) {
      message.warning('还款截止日期不能为空');
      return;
    }
    const formValues = { ...collectionData.value };
    formValues.deadline = Number(formValues.deadline);
    await getOverdueCollectionGenerateApi(formValues);
    message.success($t('base.resSuccess'));
    await settlementModalApi.close();
    await GridApi.reload();
  },
  onClosed: () => {
    collectionData.value = {};
  },
});

const collectionData = ref<OverdueCollectionInfo>({});

const currentDate = computed(() => dayjs().format('YYYY年MM月DD日'));

const generateSettlement = async (row: any) => {
  collectionData.value = cloneDeep(row);
  collectionData.value.contractSignDate = dayjs(collectionData.value.contractSignDate).format('YYYY年MM月DD日');
  collectionData.value.dueDate = dayjs(collectionData.value.dueDate).format('YYYY年MM月DD日');
  await settlementModalApi.open();
};
const FilePreviewDialogRef = ref();

const settlementDownload = (row: any) => {
  if (!row.overdueFileId) return;
  FilePreviewDialogRef.value.init(row.overdueFileId);
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['to_be_sent'].includes(row.overdueStatus)" @click="generateSettlement(row)">
            发送通知函
          </a-typography-link>
          <a-typography-link v-else @click="settlementDownload(row)"> 查看通知函 </a-typography-link>
        </a-space>
      </template>
    </Grid>

    <SettlementModal class="w-[1100px]">
      <div class="prose mx-auto max-w-none p-6 text-gray-800" style="font-size: 14px">
        <!-- 标题 -->
        <div class="mb-8 text-center text-2xl font-bold">保理融资款/融资利息支付催收函</div>

        <!-- 致语 -->
        <div class="mb-6">{{ collectionData.companyName }}：</div>

        <!-- 合同说明段落 -->
        <div class="mb-6">
          你单位于 {{ collectionData.contractSignDate }} 与我司签署了《{{ collectionData.contractName }}》（合同编号：{{
            collectionData.contractCode
          }}）。根据《{{ collectionData.contractName }}》约定，你单位应于 {{ collectionData.dueDate }} 向我司支付第【{{
            collectionData.periods
          }}】期保理融资款/融资利息，保理融资款/融资利息金额共计
          {{ collectionData.totalAmount }} 元。现你单位尚未支付保理融资款/融资利息，故我司根据《{{
            collectionData.contractName
          }}》的相关约定，特函告你单位如下事宜：
        </div>

        <!-- 催收事项列表 -->
        <div class="mb-6 list-decimal pl-6">
          <p class="mb-2">一、请你单位立即按照合同约定履行保理融资款/融资利息偿付义务。</p>
          <p class="mb-2">
            二、若上述保理融资款/融资利息于
            <a-date-picker v-model:value="collectionData.deadline" value-format="x" />前仍未到账，我公司将会根据《{{
              collectionData.contractName
            }}》的相关约定，及相关金融机构管理办法，采取法律措施，追究相应违约责任。因此造成的一切损失与责任，概由你单位承担。
          </p>
        </div>

        <!-- 告知结语 -->
        <div class="mb-12">特此告知！</div>

        <!-- 盖章与日期 -->
        <div class="text-right">
          <div class="mb-2">江西省财投商业保理有限公司</div>
          <div>{{ currentDate }}</div>
        </div>
      </div>
    </SettlementModal>
    <FilePreviewDialog
      ref="FilePreviewDialogRef"
      :preview-api="getPreviewFileExternalLink"
      :download-api="getDownloadFileLinkApi"
    />
  </Page>
</template>

<style scoped></style>
