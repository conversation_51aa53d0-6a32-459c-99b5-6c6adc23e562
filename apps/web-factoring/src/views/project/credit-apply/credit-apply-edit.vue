<script setup lang="ts">
import type { CreditApplyInfo, InitiationInfo } from '#/api';

import { reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import { cloneDeep, isEmpty, omit } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addCreditApplyApi,
  editCreditApplyApi,
  getCompanyListApi,
  getCreditApplyInfoApi,
  getCreditPricingInfoApi,
  getCreditPricingListApi,
  getProjectLimitApi,
} from '#/api';
import AccountsReceivablePool from '#/views/project/components/accounts-receivable-pool.vue';
import AccountsReceivable from '#/views/project/components/accounts-receivable.vue';
import LadderPenalty from '#/views/project/components/ladder-penalty.vue';
import Mortgage from '#/views/project/components/mortgage.vue';
import Pledge from '#/views/project/components/pledge.vue';
import RepaymentCalculation from '#/views/project/components/repayment-calculation.vue';

const emit = defineEmits(['ok', 'register']);

const dictStore = useDictStore();
const RepaymentCalculationRef = ref();
const LadderPenaltyRef = ref();
const init = async (data: CreditApplyInfo) => {
  creditApplyForm.value = {};
  if (data.id) {
    let info = data.id ? await getCreditApplyInfoApi(data.id as number) : data;
    const calculation = omit(info.calculation, 'id');
    const contract = omit(info.contract, 'id');
    info = {
      ...info,
      ...calculation,
      ...contract,
    };
    info.expectedLaunchDate = dayjs(info.expectedLaunchDate).valueOf().toString();
    info.expectedDueDate = dayjs(info.expectedDueDate).valueOf().toString();
    info.expectedFinancingDueDate = dayjs(info.expectedFinancingDueDate).valueOf().toString();
    info.contractSignDate = dayjs(info.contractSignDate).valueOf().toString();
    info.contractDueDate = dayjs(info.contractDueDate).valueOf().toString();
    creditApplyForm.value = info;
    AccountsReceivableRef.value.init(creditApplyForm.value);
    MortgageRef.value.init(creditApplyForm.value);
    PledgeRef.value.init(creditApplyForm.value);
    LadderPenaltyRef.value.init(creditApplyForm.value);
    RepaymentCalculationRef.value.init(creditApplyForm.value);
  }
  initCompanyList();
};
// 初始化关联企业数据
const initCompanyList = () => {
  const companyList = creditApplyForm.value.companyList || [];

  // 提取担保企业
  creditApplyForm.value.guarantor =
    companyList
      .filter((item: object) => item.projectCompanyType === 'guarantee')
      .map((item: object) => item.companyCode || '') || [];

  // 提取授信对象
  const creditCompany = companyList.find((item: object) => item.projectCompanyType === 'credit');
  creditApplyForm.value.credit = creditCompany?.companyCode || '';

  // 提取债权人
  creditApplyForm.value.creditor =
    companyList
      .filter((item: object) => item.projectCompanyType === 'creditor')
      .map((item: object) => item.companyCode || '') || [];
  // 提取债务人
  creditApplyForm.value.debtor =
    companyList
      .filter((item: object) => item.projectCompanyType === 'debtor')
      .map((item: object) => item.companyCode || '') || [];
  // 提取用信企业（支持多选）
  creditApplyForm.value.creditUser =
    companyList
      .filter((item: object) => item.projectCompanyType === 'using')
      .map((item: object) => item.companyCode || '') || [];
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const AccountsReceivableRef = ref();
const PledgeRef = ref();
const MortgageRef = ref();
const creditApplyForm = ref<CreditApplyInfo>({});
const loading = reactive({
  submit: false,
});
const save = async (type: string) => {
  await FormRef.value.validate();
  await AccountsReceivableRef.value.save();
  await MortgageRef.value.save();
  await PledgeRef.value.save();
  const repaymentResult = await RepaymentCalculationRef.value.save();
  if (!repaymentResult) {
    return;
  }
  await LadderPenaltyRef.value.save();
  let api = addCreditApplyApi;
  if (creditApplyForm.value.id) {
    api = editCreditApplyApi;
  }
  // 保存关联企业数据
  creditApplyForm.value.companyList = saveCompanyList();
  const formData = cloneDeep(creditApplyForm.value);
  formData.expectedLaunchDate = Number(formData.expectedLaunchDate);
  formData.expectedDueDate = Number(formData.expectedDueDate);
  formData.expectedFinancingDueDate = Number(formData.expectedFinancingDueDate);
  formData.contractSignDate = Number(formData.contractSignDate);
  formData.contractDueDate = Number(formData.contractDueDate);
  formData.calculation = { ...formData };
  formData.contract = { ...formData };
  delete formData.detailList;
  if (type === 'submit') formData.isSubmit = true;
  loading.submit = true;
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};

// 项目定价信息
const pricingInfo = ref({});

const getPricingInfo = async (id: number) => {
  pricingInfo.value = await getCreditPricingInfoApi(id as number);
};

const selectInitiation = async (_value: number, data: InitiationInfo) => {
  creditApplyForm.value.projectCreditApplyName = `${data.projectName}-用信申请`;
  await getPricingInfo(_value);
  const {
    projectId,
    projectName,
    projectType,
    creditUseCompanyCode: applyCompanyCode,
    creditUseCompanyName: applyCompanyName,
    creditCompanyCode: lenderCompanyCode,
    creditCompanyName: lenderCompanyName,
    pricingXirrRate,
    serviceFeeAmount,
  } = pricingInfo.value;
  const res = await getProjectLimitApi({ projectId: data.projectId });
  const {
    creditAmount: pricingCreditAmount,
    creditTerm: pricingCreditTerm,
    creditRate: pricingCreditRate,
    creditType: pricingCreditType,
    investedAmount,
    stockBalance,
  } = res;
  const { id: _, ...calculation } = pricingInfo.value.calculation;
  creditApplyForm.value = {
    ...creditApplyForm.value,
    ...calculation,
    projectId,
    projectName,
    projectType,
    pricingCreditAmount,
    pricingCreditTerm,
    pricingCreditRate,
    pricingCreditType,
    investedAmount,
    stockBalance,
    applyCompanyCode,
    applyCompanyName,
    pricingXirrRate,
    serviceFeeAmount,
    lenderCompanyCode,
    lenderCompanyName,
  };
  creditApplyForm.value.applyAmount = creditApplyForm.value.expectedUseAmount;
  creditApplyForm.value.expectedLaunchDate = dayjs(creditApplyForm.value.expectedLaunchDate).valueOf().toString();
  creditApplyForm.value.expectedDueDate = dayjs(creditApplyForm.value.expectedDueDate).valueOf().toString();
  LadderPenaltyRef.value.init(creditApplyForm.value);
  RepaymentCalculationRef.value.init(creditApplyForm.value);
};
const selectCompany = (_value: number, data: any, type: string) => {
  creditApplyForm.value[type] = data.label;
};
const oldFactoringType = ref<string>('');
const handleFactoringTypeChange = () => {
  oldFactoringType.value = creditApplyForm.value.factoringType || '';
  if (creditApplyForm.value.factoringType !== 'pool_factoring' && oldFactoringType.value === 'pool_factoring') {
    creditApplyForm.value.receivableRefList = [];
    AccountsReceivableRef.value.init(creditApplyForm.value);
  }
  if (creditApplyForm.value.factoringType === 'pool_factoring' && oldFactoringType.value !== 'pool_factoring') {
    creditApplyForm.value.receivableRefList = [];
    AccountsReceivableRef.value.init(creditApplyForm.value);
  }
};
const rules = {
  projectPricingId: [{ required: true, message: '请选择关联用信定价', trigger: 'change' }],
  projectCreditApplyName: [{ required: true, message: '请输入用信申请名称' }],
  expectedUseAmount: [{ required: true, message: '请输入拟用信金额' }],
  creditCompanyCode: [{ required: true, message: '请选择用信主体', trigger: 'change' }],
  targetIndustry: [{ required: true, message: '请选择投向行业', trigger: 'change' }],
  factoringType: [{ required: true, message: '请选择保理类型', trigger: 'change' }],
  contractCode: [{ required: true, message: '请输入原合同编号' }],
  contractSignDate: [{ required: true, message: '请选择原合同签署日期', trigger: 'change' }],
  contractDueDate: [{ required: true, message: '请选择原合同到期日期', trigger: 'change' }],
  contractAmount: [{ required: true, message: '请输入原合同金额' }],
  contractRecycleAmount: [{ required: true, message: '请输入原合同已回收本金' }],
  compositeYieldRate: [{ required: true, message: '请输入综合收益率' }],
  serviceFeeAmount: [{ required: true, message: '请输入服务费' }],
  applyCompanyCode: [{ required: true, message: '请选择用信申请人', trigger: 'change' }],
  applyAmount: [{ required: true, message: '请输入用信申请金额' }],
  gracePeriodDays: [{ required: true, message: '请输入宽限期天数' }],
  gracePeriodRate: [{ required: true, message: '请输入宽限期费率' }],
  penaltyType: [{ required: true, message: '请选择罚息类型', trigger: 'change' }],
  drawingMethod: [{ required: true, message: '请选择提用方式', trigger: 'change' }],
  penaltyInterestRate: [{ required: true, message: '请输入固定罚息利率' }],
  planningMethod: [{ required: true, message: '请选择还本付息计划规划方式', trigger: 'change' }],
  expectedLaunchDate: [{ required: true, message: '请选择预计业务投放日', trigger: 'change' }],
  expectedDueDate: [{ required: true, message: '请选择预估最后还款日', trigger: 'change' }],
  expectedFinancingDueDate: [{ required: true, message: '请选择预估融资到期日', trigger: 'change' }],
  principalRepaymentMethod: [{ required: true, message: '请选择还本方式', trigger: 'change' }],
  interestRepaymentMethod: [{ required: true, message: '请选择还息方式', trigger: 'change' }],
  principalPeriod: [{ required: true, message: '请选择分期还本频次', trigger: 'change' }],
  interestPeriod: [{ required: true, message: '请选择分期还息频次', trigger: 'change' }],
  repayPrincipalDay: [{ required: true, message: '请选择默认当期还本日', trigger: 'change' }],
  repayInterestDay: [{ required: true, message: '请选择默认当期还息日', trigger: 'change' }],
  creditCompany: [{ required: true, message: '请选择授信企业', trigger: 'change' }],
  isSupportRealEconomy: [{ required: true, message: '请选择是否支持实体经济', trigger: 'change' }],
  isProvincialKeyIndustry: [{ required: true, message: '请选择是否为省重点产业', trigger: 'change' }],
  creditor: [{ required: true, message: '请选择债权人', trigger: 'change' }],
};
const changeReceivable = () => {
  if (!isEmpty(creditApplyForm.value.receivableRefList) && creditApplyForm.value.applyAmount) {
    let receivableAmount = new BigNumber(0);
    creditApplyForm.value.receivableRefList.forEach((item) => {
      receivableAmount = receivableAmount.plus(
        new BigNumber(
          item[creditApplyForm.value.factoringType === 'pool_factoring' ? 'poolTotalAmount' : 'receivableAmount'] || 0,
        ),
      );
    });
    const applyAmount = new BigNumber(creditApplyForm.value.applyAmount);
    creditApplyForm.value.financingRatio = applyAmount
      .div(receivableAmount)
      .times(100)
      .decimalPlaces(2, BigNumber.ROUND_HALF_UP)
      .toNumber();
  } else {
    creditApplyForm.value.financingRatio = 0;
  }
};
// 保存数据时转换关联企业数据
const saveCompanyList = (): ProjectCompanyBO[] => {
  const companyList: ProjectCompanyBO[] = [];

  // 添加担保企业
  if (creditApplyForm.value.guarantor?.length > 0) {
    creditApplyForm.value.guarantor.forEach((companyCode: string) => {
      if (companyCode) {
        companyList.push({
          companyCode,
          companyName: getCompanyLabel(companyCode),
          projectCompanyType: 'guarantee',
        });
      }
    });
  }

  // 添加债权人
  if (creditApplyForm.value.creditor?.length > 0) {
    creditApplyForm.value.creditor.forEach((companyCode: string) => {
      if (companyCode) {
        companyList.push({
          companyCode,
          companyName: getCompanyLabel(companyCode),
          projectCompanyType: 'creditor',
        });
      }
    });
  }

  // 添加债务人
  if (creditApplyForm.value.debtor?.length > 0) {
    creditApplyForm.value.debtor.forEach((companyCode: string) => {
      if (companyCode) {
        companyList.push({
          companyCode,
          companyName: getCompanyLabel(companyCode),
          projectCompanyType: 'debtor',
        });
      }
    });
  }

  return companyList;
};
// 公司列表
const companyOptions = ref<{ companyCode: string; companyName: string }[]>([]);

const loadCompanyOptions = async () => {
  companyOptions.value = await getCompanyListApi();
};

// 初始化时加载公司列表
loadCompanyOptions();

// 获取公司名称
const getCompanyLabel = (companyCode: string): string => {
  if (!companyCode) return '';

  // 从缓存中查找
  const company = companyOptions.value.find((item) => item.companyCode === companyCode);

  return company?.companyName || '';
};
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 4 }, wrapperCol: { span: 20 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" title="用信申请" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="creditApplyForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联用信定价" name="projectPricingId">
              <ApiComponent
                v-model="creditApplyForm.projectPricingId as unknown as string"
                :component="Select"
                :api="getCreditPricingListApi"
                :params="{ status: 'COMPLETED', isProjectPricingCompleted: 1, projectActiveFlag: 1 }"
                label-field="pricingName"
                value-field="id"
                model-prop-name="value"
                @change="selectInitiation"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目名称" name="projectName">
              <a-input v-model:value="creditApplyForm.projectName" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="用信申请名称" name="projectCreditApplyName">
              <a-input v-model:value="creditApplyForm.projectCreditApplyName" />
            </a-form-item>
          </a-col>
          <!--          <a-col v-bind="colSpan">-->
          <!--            <a-form-item label="授信企业" name="creditCompanyCode">-->
          <!--              <ApiComponent-->
          <!--                v-model="creditApplyForm.creditCompanyCode as unknown as string"-->
          <!--                :component="Select"-->
          <!--                :api="getCompanyListApi"-->
          <!--                label-field="companyName"-->
          <!--                value-field="companyCode"-->
          <!--                model-prop-name="value"-->
          <!--                @change="(value: number, data: any) => selectCompany(value, data, 'creditCompanyName')"-->
          <!--              />-->
          <!--            </a-form-item>-->
          <!--          </a-col>-->
          <a-col v-bind="colSpan">
            <a-form-item label="投向行业" name="targetIndustry">
              <a-select
                v-model:value="creditApplyForm.targetIndustry"
                :options="dictStore.getDictList('FCT_TARGET_INDUSTRY')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="是否支持实体经济" name="isSupportRealEconomy">
              <a-radio-group
                v-model:value="creditApplyForm.isSupportRealEconomy"
                :options="dictStore.getDictList('baseBooleanType')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="是否为省重点产业" name="isProvincialKeyIndustry">
              <a-radio-group
                v-model:value="creditApplyForm.isProvincialKeyIndustry"
                :options="dictStore.getDictList('baseBooleanType')"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" name="remarks" v-bind="fullProp">
              <a-textarea v-model:value="creditApplyForm.remarks" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 授信批复方案 -->
        <BasicCaption content="授信批复方案" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="授信金额（元）" name="pricingCreditAmount">
              <a-input v-model:value="creditApplyForm.pricingCreditAmount" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信期限（月）" name="pricingCreditTerm">
              <a-input v-model:value="creditApplyForm.pricingCreditTerm" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信费率（%/年）" name="pricingCreditRate">
              <a-input v-model:value="creditApplyForm.pricingCreditRate" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信方式">
              {{ dictStore.formatter(creditApplyForm.pricingCreditType, 'FCT_CREDIT_TYPE') }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目已投放金额（元）" name="investedAmount">
              <a-input v-model:value="creditApplyForm.investedAmount" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目存量余额（元）" name="stockBalance">
              <a-input v-model:value="creditApplyForm.stockBalance" disabled />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 业务类型 -->
        <BasicCaption content="业务类型" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="保理类型" name="factoringType">
              <a-select
                v-model:value="creditApplyForm.factoringType"
                :options="dictStore.getDictList('FCT_FACTORING_TYPE')"
                @change="handleFactoringTypeChange"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="支持操作模式" name="supportMode">
              <a-radio-group
                v-model:value="creditApplyForm.supportMode"
                :options="dictStore.getDictList('FCT_SUPPORT_MODE')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="支持保理方向" name="factoringDirection">
              <a-radio-group
                v-model:value="creditApplyForm.factoringDirection"
                :options="dictStore.getDictList('FCT_FACTORING_DIRECTION')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="追索权要求" name="recourseRequired">
              <a-radio-group
                v-model:value="creditApplyForm.recourseRequired"
                :options="dictStore.getDictList('FCT_RECOURES_REQUIRED')"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <AccountsReceivable
          v-if="creditApplyForm.factoringType !== 'pool_factoring'"
          ref="AccountsReceivableRef"
          v-model="creditApplyForm"
          @change-receivable="changeReceivable"
        />
        <AccountsReceivablePool
          v-if="creditApplyForm.factoringType === 'pool_factoring'"
          ref="AccountsReceivableRef"
          v-model="creditApplyForm"
          @change-receivable="changeReceivable"
        />
        <!-- 原项目合同概况 -->
        <template v-if="['refinance_factoring', 'lease_factoring'].includes(creditApplyForm.factoringType)">
          <BasicCaption content="原项目合同概况" />
          <a-row class="mt-5">
            <a-col v-bind="colSpan">
              <a-form-item label="原合同编号" name="contractCode">
                <a-input v-model:value="creditApplyForm.contractCode" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="原合同签署日期" name="contractSignDate">
                <a-date-picker v-model:value="creditApplyForm.contractSignDate" value-format="x" class="w-full" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="原合同到期日期" name="contractDueDate">
                <a-date-picker v-model:value="creditApplyForm.contractDueDate" value-format="x" class="w-full" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="原合同金额（元）" name="contractAmount">
                <a-input v-model:value="creditApplyForm.contractAmount" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="原合同已回收本金（元）" name="contractRecycleAmount">
                <a-input v-model:value="creditApplyForm.contractRecycleAmount" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="剩余权益价值金额（元）" name="contractRemainingAmount">
                <a-input v-model:value="creditApplyForm.contractRemainingAmount" />
              </a-form-item>
            </a-col>
            <template v-if="creditApplyForm.factoringType === 'refinance_factoring'">
              <a-col v-bind="colSpan">
                <a-form-item label="保理商" name="contractFactorCode">
                  <ApiComponent
                    v-model="creditApplyForm.contractFactorCode as unknown as string"
                    :component="Select"
                    :api="getCompanyListApi"
                    label-field="companyName"
                    value-field="companyCode"
                    model-prop-name="value"
                    @change="(value: number, data: any) => selectCompany(value, data, 'contractFactor')"
                  />
                </a-form-item>
              </a-col>
              <a-col v-bind="colSpan">
                <a-form-item label="债权人" name="contractCreditorCode">
                  <ApiComponent
                    v-model="creditApplyForm.contractCreditorCode as unknown as string"
                    :component="Select"
                    :api="getCompanyListApi"
                    label-field="companyName"
                    value-field="companyCode"
                    model-prop-name="value"
                    @change="(value: number, data: any) => selectCompany(value, data, 'contractCreditor')"
                  />
                </a-form-item>
              </a-col>
            </template>
            <template v-if="creditApplyForm.factoringType === 'lease_factoring'">
              <a-col v-bind="colSpan">
                <a-form-item label="出租人" name="contractLessorCode">
                  <ApiComponent
                    v-model="creditApplyForm.contractLessorCode as unknown as string"
                    :component="Select"
                    :api="getCompanyListApi"
                    label-field="companyName"
                    value-field="companyCode"
                    model-prop-name="value"
                    @change="(value: number, data: any) => selectCompany(value, data, 'contractLessor')"
                  />
                </a-form-item>
              </a-col>
              <a-col v-bind="colSpan">
                <a-form-item label="承租人" name="contractLesseeCode">
                  <ApiComponent
                    v-model="creditApplyForm.contractLesseeCode as unknown as string"
                    :component="Select"
                    :api="getCompanyListApi"
                    label-field="companyName"
                    value-field="companyCode"
                    model-prop-name="value"
                    @change="(value: number, data: any) => selectCompany(value, data, 'contractLessee')"
                  />
                </a-form-item>
              </a-col>
            </template>
            <a-col v-bind="colSpan">
              <a-form-item label="底层资产" name="contractAssets">
                <a-input v-model:value="creditApplyForm.contractAssets" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="拟转让应收账款概况" name="contractTransferredDesc">
                <a-input v-model:value="creditApplyForm.contractTransferredDesc" />
              </a-form-item>
            </a-col>
          </a-row>
        </template>
        <!-- 交易主体信息 -->
        <BasicCaption content="交易主体信息" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="债权人" name="creditor">
              <ApiComponent
                v-model="creditApplyForm.creditor as unknown as any"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                mode="multiple"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="担保人" name="guarantor">
              <ApiComponent
                v-model="creditApplyForm.guarantor as unknown as string"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                mode="multiple"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="债务人" name="debtor">
              <ApiComponent
                v-model="creditApplyForm.debtor as unknown as any"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                mode="multiple"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <Mortgage ref="MortgageRef" v-model="creditApplyForm" />
        <Pledge ref="PledgeRef" v-model="creditApplyForm" />
        <!-- 用信核心要素 -->
        <BasicCaption content="用信核心要素" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="放款主体" name="lenderCompanyCode">
              <ApiComponent
                v-model="creditApplyForm.lenderCompanyCode as unknown as string"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="用信申请人" name="applyCompanyCode">
              <ApiComponent
                v-model="creditApplyForm.applyCompanyCode as unknown as string"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                @change="(value: number, data: any) => selectCompany(value, data, 'applyCompanyName')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="用信申请金额（元）" name="applyAmount">
              <a-input v-model:value="creditApplyForm.applyAmount" @blur="changeReceivable" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="融资比例（%）" name="financingRatio">
              <a-input v-model:value="creditApplyForm.financingRatio" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="用信定价综合收益率（%/年）" name="pricingXirrRate">
              <a-input v-model:value="creditApplyForm.pricingXirrRate" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="预估融资到期日" name="expectedFinancingDueDate">
              <a-date-picker v-model:value="creditApplyForm.expectedFinancingDueDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="服务费金额（元）" name="serviceFeeAmount">
              <a-input v-model:value="creditApplyForm.serviceFeeAmount" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="宽限期天数（日）" name="gracePeriodDays">
              <a-input v-model:value="creditApplyForm.gracePeriodDays" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="宽限期费率（%/年）" name="gracePeriodRate">
              <a-input v-model:value="creditApplyForm.gracePeriodRate" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="提用方式" name="drawingMethod">
              <a-select
                v-model:value="creditApplyForm.drawingMethod"
                :options="dictStore.getDictList('FCT_DRAWING_METHOD')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="增信措施" name="creditEnhancementDesc">
              <a-input v-model:value="creditApplyForm.creditEnhancementDesc" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="第一还款来源" name="firstRepaySource">
              <a-input v-model:value="creditApplyForm.firstRepaySource" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="补充还款来源" name="supplementRepaySource">
              <a-input v-model:value="creditApplyForm.supplementRepaySource" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="罚息类型" name="penaltyType">
              <a-radio-group
                v-model:value="creditApplyForm.penaltyType"
                :options="dictStore.getDictList('FCT_PENALTY_TYPE')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan" v-if="creditApplyForm.penaltyType === 'fixed'">
            <a-form-item label="固定罚息利率（%）" name="penaltyInterestRate">
              <a-input v-model:value="creditApplyForm.penaltyInterestRate" />
            </a-form-item>
          </a-col>
        </a-row>
        <LadderPenalty
          ref="LadderPenaltyRef"
          v-show="creditApplyForm.penaltyType === 'ladder'"
          v-model="creditApplyForm"
        />
        <RepaymentCalculation ref="RepaymentCalculationRef" v-model="creditApplyForm" calculation-type="CreditApply" />
        <BaseAttachmentList
          v-model="creditApplyForm.attachmentList"
          :business-id="creditApplyForm.id"
          business-type="FCT_PROJECT_CREDIT_APPLY"
          edit-mode
        />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
