<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { CreditApplyInfo } from '#/api';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Dropdown, Menu, MenuItem, message, Space, TypographyLink } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  creditUploadApplication,
  creditUploadOperation,
  creditUploadRisk,
  delCreditApplyApi,
  getCreditApplyPageListApi,
} from '#/api';

import CreditApplyDetail from './credit-apply-detail.vue';
import CreditApplyEdit from './credit-apply-edit.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCreditApplyName',
      label: '用信申请名称',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Input',
      fieldName: 'applyCompanyName',
      label: '用信企业',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectCreditApplyName', title: '用信申请名称', minWidth: 200 },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    { field: 'creditCompanyName', title: '授信企业', minWidth: 200 },
    { field: 'applyCompanyName', title: '用信主体', minWidth: 200 },
    { field: 'applyAmount', title: '用信申请金额（元）', minWidth: 160 },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PRICING_STATUS',
        },
      },
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 140,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getCreditApplyPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: CreditApplyInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: CreditApplyInfo) => {
  openDetailPopup(true, row);
};
const del = (row: CreditApplyInfo) => {
  const id = row.id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该用信申请，是否继续？',
    async onOk() {
      await delCreditApplyApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const uploadInfo = (row: any, type: string) => {
  uploadForm.value.type = type;
  uploadForm.value.id = row.id;
  uploadForm.value.label = {
    application: '上传用信申请报告',
    risk: '上传风控复核报告',
    operation: '上传运营复核报告',
  }[type];
  uploadForm.value.businessType = {
    application: 'FCT_PROJECT_CREDIT_APPLY_APPLICATION',
    risk: 'FCT_PROJECT_CREDIT_APPLY_RISK',
    operation: 'FCT_PROJECT_CREDIT_APPLY_OPERATION',
  }[type];
  modalApi.open();
};
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    const api = {
      application: creditUploadApplication,
      risk: creditUploadRisk,
      operation: creditUploadOperation,
    }[uploadForm.value.type];
    await api(uploadForm.value);
    message.success($t('base.resSuccess'));
    await modalApi.close();
  },
  onClosed: () => {
    uploadForm.value = {
      businessType: '',
    };
  },
});
const uploadForm = ref({
  businessType: '',
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add">
            {{ $t('base.add') }}
          </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
          <a-typography-link type="danger" v-if="['SUBMIT'].includes(row.status)" @click="del(row)">
            {{ $t('base.del') }}
          </a-typography-link>
          <Dropdown v-if="['EFFECTIVE'].includes(row.status)">
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <MenuItem key="application" @click="uploadInfo(row, 'application')"> 上传用信申请报告 </MenuItem>
                <MenuItem key="risk" @click="uploadInfo(row, 'risk')"> 上传风控复核报告 </MenuItem>
                <MenuItem key="operation" @click="uploadInfo(row, 'operation')"> 上传运营复核报告 </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </a-space>
      </template>
    </Grid>
    <CreditApplyEdit @register="registerForm" @ok="editSuccess" />
    <CreditApplyDetail @register="registerDetail" />
    <Modal :title="uploadForm.label" class="w-[800px]">
      <BaseAttachmentList
        v-model="uploadForm.attachmentList"
        :business-id="uploadForm.id"
        :business-type="uploadForm.businessType"
        edit-mode
      />
    </Modal>
  </Page>
</template>

<style></style>
