<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref } from 'vue';

// 引入数据类型
import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { exportLedgerFinancialApi, getFinanceListApi } from '#/api';

// 表单配置：保持原有搜索条件
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '融资客户',
    },
    {
      component: 'RangePicker',
      fieldName: 'planDate',
      label: '起息日',
    },
    {
      component: 'RangePicker',
      fieldName: 'settlementDate',
      label: '回款日',
    },
  ],
  fieldMappingTime: [
    ['planDate', ['beginPlanDate', 'endPlanDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
    ['settlementDate', ['beginSettlementDate', 'endSettlementDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});

// 表格配置：按接口返回字段和原型顺序调整
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectName', title: '项目名称', minWidth: 160 },
    { field: 'companyName', title: '融资客户', minWidth: 160 },
    { field: 'launchDate', title: '投放日期', minWidth: 140, formatter: 'formatDate' },
    { field: 'launchAmount', title: '投放金额（元）', minWidth: 160 },
    { field: 'balanceAmount', title: '本金余额（元）', minWidth: 160 },
    { field: 'contractRate', title: '合同年利率（%）', minWidth: 160 },
    { field: 'planDate', title: '起息日', minWidth: 140, formatter: 'formatDate' },
    { field: 'settlementDate', title: '回款日', minWidth: 140, formatter: 'formatDate' },
    { field: 'principalAmount', title: '应收本金（元）', minWidth: 160 },
    { field: 'interestAmount', title: '应收利息（元）', minWidth: 160 },
    { field: 'serviceAmount', title: '服务费（元）', minWidth: 180 },
    { field: 'totalAmount', title: '应回款总额（元）', minWidth: 180 },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        searchForm.value = formValues;
        return await getFinanceListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
// 解构表格 API（用于刷新表格）
const [Grid] = useVbenVxeGrid({ formOptions, gridOptions });
const searchForm = ref<any>({});
// 单据导出
const documentExport = async () => {
  await exportLedgerFinancialApi(searchForm.value);
  message.success($t('base.resSuccess'));
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="documentExport"> 导出 </a-button>
        </a-space>
      </template>
    </Grid>
  </Page>
</template>

<style></style>
