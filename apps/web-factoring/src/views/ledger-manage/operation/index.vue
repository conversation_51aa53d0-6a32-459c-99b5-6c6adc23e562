<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref } from 'vue';

// 引入数据类型
import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { exportLedgerOperationApi, getOperationListApi } from '#/api';

const dictStore = useDictStore();
// 表单配置：保持原有搜索条件
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'DatePicker',
      fieldName: 'searchYear',
      label: '台账年份',
      componentProps: {
        picker: 'year',
      },
      defaultValue: dayjs(),
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'projectType',
      label: '项目类型',
      componentProps: {
        options: dictStore.getDictList('FCT_PROJECT_TYPE'),
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'creditBeginDate',
      label: '决策日期',
    },
    {
      component: 'RangePicker',
      fieldName: 'creditEndDate',
      label: '授信到期日',
    },
    {
      component: 'RangePicker',
      fieldName: 'creditApplyApprovalDate',
      label: '用信审批日期',
    },
    {
      component: 'RangePicker',
      fieldName: 'investDate',
      label: '投放日期',
    },
    {
      component: 'RangePicker',
      fieldName: 'dueDate',
      label: '结束日期',
    },
    {
      component: 'RangePicker',
      fieldName: 'settlementDate',
      label: '结清日期',
    },
    {
      component: 'Input',
      fieldName: 'cityName',
      label: '地级市',
    },
    {
      component: 'Input',
      fieldName: 'districtName',
      label: '区县',
    },
    {
      component: 'Select',
      fieldName: 'survivalStatus',
      label: '存续状态',
      componentProps: {
        options: dictStore.getDictList('FCT_SURVIVAL_STATUS'),
      },
    },
  ],
  fieldMappingTime: [
    ['creditBeginDate', ['beginCreditBeginDate', 'endCreditBeginDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
    ['creditEndDate', ['beginCreditEndDate', 'endCreditEndDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
    [
      'creditApplyApprovalDate',
      ['beginCreditApplyApprovalDate', 'endCreditApplyApprovalDate'],
      ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
    ],
    ['investDate', ['beginInvestDate', 'endInvestDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
    ['dueDate', ['beginDueDate', 'endDueDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
    ['settlementDate', ['beginSettlementDate', 'endSettlementDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
  ],
  commonConfig: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  },
});

// 表格配置：按接口返回字段和顺序调整
const gridOptions: VxeTableGridOptions = {
  columns: [
    {
      field: 'projectType',
      title: '项目类型',
      minWidth: 120,
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PROJECT_TYPE',
        },
      },
    },
    { field: 'projectName', title: '项目名称', minWidth: 240 },
    { field: 'creditApplyName', title: '用信名称', minWidth: 240 },
    { field: 'repaymentPlanCode', title: '还款计划编号', minWidth: 240 },
    { field: 'creditBeginDate', title: '决策日期', minWidth: 140, formatter: 'formatDate' },
    { field: 'creditTerm', title: '授信期限（月）', minWidth: 140 },
    { field: 'creditEndDate', title: '授信到期日', minWidth: 140, formatter: 'formatDate' },
    { field: 'creditAmount', title: '授信金额（元）', minWidth: 160 },
    { field: 'creditApplyApprovalDate', title: '用信审批日期', minWidth: 160, formatter: 'formatDate' },
    { field: 'investAmount', title: '累计投放金额（元）', minWidth: 160 },
    { field: 'factoringType', title: '业务类型', minWidth: 120 },
    { field: 'survivalStatus', title: '存续状态', minWidth: 120 },
    { field: 'cityName', title: '地市', minWidth: 100 },
    { field: 'districtName', title: '区县', minWidth: 100 },
    { field: 'targetIndustry', title: '投向行业', minWidth: 140 },
    { field: 'creditorName', title: '债权人', minWidth: 140 },
    { field: 'debtorName', title: '债务人', minWidth: 140 },
    { field: 'guarantorName', title: '担保人', minWidth: 140 },
    { field: 'creditEnhancementMethod', title: '增信措施', minWidth: 160 },
    { field: 'contractTerm', title: '合同期限（月）', minWidth: 140 },
    { field: 'investDate', title: '投放日期', minWidth: 140, formatter: 'formatDate' },
    { field: 'dueDate', title: '结束日期', minWidth: 140, formatter: 'formatDate' },
    { field: 'settlementDate', title: '结清日期', minWidth: 140, formatter: 'formatDate' },
    { field: 'businessUserNames', title: '业务经理', minWidth: 140 },
    { field: 'operationsUserNames', title: '运营经理', minWidth: 140 },
    { field: 'riskUserNames', title: '风控经理', minWidth: 140 },
    { field: 'financeUserNames', title: '财务经理', minWidth: 140 },
    { field: 'beginYearBusinessScale', title: '年初业务规模（元）', minWidth: 160 },
    { field: 'thisYearAccumulatedInvest', title: '本年累计新增（元）', minWidth: 160 },
    { field: 'thisYearAccumulatedCollection', title: '本年累计收回（元）', minWidth: 160 },
    { field: 'projectAccumulatedCollection', title: '项目累计收回（元）', minWidth: 160 },
    { field: 'finalScale', title: '期末规模（元）', minWidth: 140 },
    { field: 'januaryCollection', title: '1月收回（元）', minWidth: 140 },
    { field: 'januaryInvest', title: '1月新增（元）', minWidth: 140 },
    { field: 'februaryCollection', title: '2月收回（元）', minWidth: 140 },
    { field: 'februaryInvest', title: '2月新增（元）', minWidth: 140 },
    { field: 'marchCollection', title: '3月收回（元）', minWidth: 140 },
    { field: 'marchInvest', title: '3月新增（元）', minWidth: 140 },
    { field: 'aprilCollection', title: '4月收回（元）', minWidth: 140 },
    { field: 'aprilInvest', title: '4月新增（元）', minWidth: 140 },
    { field: 'mayCollection', title: '5月收回（元）', minWidth: 140 },
    { field: 'mayInvest', title: '5月新增（元）', minWidth: 140 },
    { field: 'juneCollection', title: '6月收回（元）', minWidth: 140 },
    { field: 'juneInvest', title: '6月新增（元）', minWidth: 140 },
    { field: 'julyCollection', title: '7月收回（元）', minWidth: 140 },
    { field: 'julyInvest', title: '7月新增（元）', minWidth: 140 },
    { field: 'augustCollection', title: '8月收回（元）', minWidth: 140 },
    { field: 'augustInvest', title: '8月新增（元）', minWidth: 140 },
    { field: 'septemberCollection', title: '9月收回（元）', minWidth: 140 },
    { field: 'septemberInvest', title: '9月新增（元）', minWidth: 140 },
    { field: 'octoberCollection', title: '10月收回（元）', minWidth: 140 },
    { field: 'octoberInvest', title: '10月新增（元）', minWidth: 140 },
    { field: 'novemberCollection', title: '11月收回（元）', minWidth: 140 },
    { field: 'novemberInvest', title: '11月新增（元）', minWidth: 140 },
    { field: 'decemberCollection', title: '12月收回（元）', minWidth: 140 },
    { field: 'decemberInvest', title: '12月新增（元）', minWidth: 140 },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        searchForm.value = formValues;
        return await getOperationListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
          searchYear: dayjs(formValues.searchYear).format('YYYY'),
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};

// 解构表格 API（用于刷新表格）
const [Grid] = useVbenVxeGrid({ formOptions, gridOptions });
const searchForm = ref<any>({});
// 单据导出
const documentExport = async () => {
  await exportLedgerOperationApi(searchForm.value);
  message.success($t('base.resSuccess'));
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="documentExport"> 导出 </a-button>
        </a-space>
      </template>
    </Grid>
  </Page>
</template>

<style></style>
