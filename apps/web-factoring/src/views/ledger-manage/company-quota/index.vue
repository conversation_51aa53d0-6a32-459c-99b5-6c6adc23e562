<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref } from 'vue';

// 引入数据类型
import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { exportLedgerCompanyApi, getCompanyQuotaListApi } from '#/api';

// 表单配置：保持原有搜索条件
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '企业名称',
    },
    {
      component: 'Input',
      fieldName: 'cityName',
      label: '地级市',
    },
    {
      component: 'Input',
      fieldName: 'districtName',
      label: '区县',
    },
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});

// 表格配置：按接口返回字段和原型顺序调整
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'companyName', title: '企业名称', minWidth: 160 },
    { field: 'cityName', title: '地级市', minWidth: 120 },
    { field: 'districtName', title: '区县', minWidth: 120 },
    { field: 'beginDate', title: '决策时间', minWidth: 140 },
    { field: 'creditTerm', title: '授信期限（月）', minWidth: 140 },
    { field: 'endDate', title: '授信到期日', minWidth: 140 },
    { field: 'creditType', title: '授信方式', minWidth: 140 },
    { field: 'companyCreditAmount', title: '授信额度（元）', minWidth: 160 },
    { field: 'companyAccumulatedUsedAmount', title: '累计用信额度（元）', minWidth: 180 },
    { field: 'companySettlementAmount', title: '已结清额度（元）', minWidth: 160 },
    { field: 'companyStockBalanceAmount', title: '存量业务余额（元）', minWidth: 180 },
    { field: 'companyAvailableAmount', title: '可用额度（元）', minWidth: 160 },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        searchForm.value = formValues;
        return await getCompanyQuotaListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
// 解构表格 API（用于刷新表格）
const [Grid] = useVbenVxeGrid({ formOptions, gridOptions });
const searchForm = ref<any>({});
// 单据导出
const documentExport = async () => {
  await exportLedgerCompanyApi(searchForm.value);
  message.success($t('base.resSuccess'));
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="documentExport"> 导出 </a-button>
        </a-space>
      </template>
    </Grid>
  </Page>
</template>

<style></style>
