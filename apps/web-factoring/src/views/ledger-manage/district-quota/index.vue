<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref } from 'vue';

// 引入数据类型
import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { exportLedgerRegionApi, getDistrictQuotaListApi } from '#/api';
// 表单配置：修改搜索条件为项目名称、用信申请名称、付款申请名称
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'cityName',
      label: '地级市',
    },
    {
      component: 'Input',
      fieldName: 'districtName',
      label: '区县',
    },
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});

// 表格配置：按要求调整列顺序和字段映射
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'cityName', title: '地级市', minWidth: 120 },
    { field: 'districtName', title: '区县', minWidth: 120 },
    { field: 'regionBudgetAmount', title: '最近年度一般公共预算收入（元）', minWidth: 240 },
    { field: 'regionCreditRatio', title: '区域授信比例（%）', minWidth: 160 },
    { field: 'regionCreditAmount', title: '地区额度上限（元）', minWidth: 160 },
    { field: 'regionUsedAmount', title: '已用信额度（元）', minWidth: 160 },
    { field: 'regionSettlementAmount', title: '已结清额度（元）', minWidth: 160 },
    { field: 'regionStockBalanceAmount', title: '存量业务余额（元）', minWidth: 160 },
    { field: 'regionAvailableAmount', title: '可用额度余额（元）', minWidth: 160 },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        searchForm.value = formValues;
        return await getDistrictQuotaListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
// 解构表格 API（用于刷新表格）
const [Grid] = useVbenVxeGrid({ formOptions, gridOptions });
const searchForm = ref<any>({});
// 单据导出
const documentExport = async () => {
  await exportLedgerRegionApi(searchForm.value);
  message.success($t('base.resSuccess'));
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="documentExport"> 导出 </a-button>
        </a-space>
      </template>
    </Grid>
  </Page>
</template>

<style></style>
