<script setup lang="ts">
import type { BatchSettingData } from '../components/comm';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { OrderInfo, OrderProductInfo } from '#/api';

import { ref } from 'vue';

import { getCombinedErrorMessagesString } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { exportProductApi } from '#/api';
import { formatFun, plusFun } from '#/utils/calculate';

import BatchSettingModal from '../components/batchSettingModal.vue';
import { baseGridOptions, numBatchSettingConfirm, numCalculateRow } from '../components/comm';

const props = defineProps<{ propsData: OrderInfo }>();
const emit = defineEmits(['emitReset']);
const productList = ref<OrderProductInfo[]>([]);
const batchSettingModal = ref();
// 新增初始化方法
const setProductData = (data: OrderProductInfo[]) => {
  const formattedData = data.map((item) => {
    return {
      ...item,
      amountWithTax: formatFun(item.amountWithTax),
      taxAmount: formatFun(item.taxAmount),
      amountWithoutTax: formatFun(item.amountWithoutTax),
      priceWithTax: formatFun(item.priceWithTax),
      priceWithoutTax: formatFun(item.priceWithoutTax),
    };
  });
  productList.value = formattedData;
  if (gridApi.grid) {
    gridApi.grid.reloadData(productList.value);
  }
};
// 表格配置
const gridOptions = {
  ...baseGridOptions,
  data: productList.value,
  editRules: {
    returnQuantity: [
      { required: true, message: '请输入退货重量' },
      { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入正确重量,最多4位小数' },
    ],
    taxRate: [
      { required: true, message: '请输入税率' },
      { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入正确税率,最多4位小数' },
    ],
    priceWithTax: [
      { required: true, message: '请输入含税单价' },
      { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确含税单价,最多2位小数' },
    ],
    priceWithoutTax: [
      { required: true, message: '请输入不含税单价' },
      { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确不含税单价,最多2位小数' },
    ],
  },
  columns: [
    { field: 'checkbox', type: 'checkbox', width: '50px', fixed: 'left' },
    {
      field: 'categoryName',
      title: '商品分类',
    },
    {
      field: 'productName',
      title: '商品名称',
      slots: { edit: 'edit_productName' },
    },
    {
      field: 'productCode',
      title: '商品编码',
      slots: { edit: 'edit_spuCode' },
    },
    {
      field: 'productAlias',
      title: '商品别名',
      slots: { edit: 'edit_productAlias' },
    },
    {
      field: 'specifications',
      title: '规格型号',
      slots: { edit: 'edit_skuName' },
    },
    {
      field: 'measureUnit',
      title: '计量单位',
      slots: { edit: 'edit_measureUnit' },
    },
    {
      field: 'brandName',
      title: '牌号/材质',
      slots: { edit: 'edit_brandName' },
    },
    {
      field: 'originName',
      title: '产地/厂商',
      slots: { edit: 'edit_originName' },
    },
    {
      field: 'originalQuantity',
      title: '销售重量',
      slots: { edit: 'edit_originalQuantity' },
    },
    {
      field: 'returnQuantity',
      title: '退货重量',
      slots: { default: 'edit_returnQuantity' },
    },
    {
      field: 'priceWithTax',
      title: '含税单价',
      slots: { default: 'edit_priceWithTax' },
    },
    {
      field: 'taxRate',
      title: '税率(%)',
      slots: { default: 'edit_taxRate' },
    },
    {
      field: 'amountWithTax',
      title: '含税金额',
      slots: { edit: 'edit_amountWithTax' },
    },
    {
      field: 'taxAmount',
      title: '税额',
      slots: { edit: 'edit_taxAmount' },
    },
    {
      field: 'amountWithoutTax',
      title: '不含税金额',
      slots: { edit: 'edit_amountWithoutTax' },
    },
    {
      field: 'priceWithoutTax',
      title: '不含税单价',
      slots: { default: 'edit_priceWithoutTax' },
    },
    {
      field: 'itemNumber',
      title: '采购订单行号',
      type: 'seq',
    },
    {
      field: 'remarks',
      title: '备注',
      slots: { default: 'edit_remarks' },
      minWidth: '160px',
    },
  ],
  footerMethod({ $grid }) {
    const data = $grid?.getTableData().visibleData || [];
    let amountWithTax = 0;
    let taxAmount = 0;
    let amountWithoutTax = 0;
    data.forEach((item) => {
      amountWithTax = plusFun(amountWithTax, item.amountWithTax || 0);
      taxAmount = plusFun(taxAmount, item.taxAmount || 0);
      amountWithoutTax = plusFun(amountWithoutTax, item.amountWithoutTax || 0);
    });
    const footerRow = {
      checkbox: '合计',
      amountWithTax: formatFun(amountWithTax),
      taxAmount: formatFun(taxAmount),
      amountWithoutTax: formatFun(amountWithoutTax),
    };
    return [footerRow];
  },
} as VxeTableGridOptions;

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
// 删除行
const removeAccount = async () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      $grid.removeCheckboxRow();
      message.success('删除成功');
    } else {
      message.warning('请选择数据');
    }
  }
};

// 暴露方法给父组件
defineExpose({
  async getProductData() {
    const $grid = gridApi.grid;
    if ($grid) {
      const errMap = await $grid.validate(true);
      if (errMap) {
        const errMessage = getCombinedErrorMessagesString(errMap);
        if (errMessage) {
          message.error(errMessage);
          return null;
        }
      }
      const { visibleData } = gridApi.grid.getTableData();
      const processedData = visibleData.map((item, index) => ({
        ...item,
        salesOrderItemNumber: item.itemNumber,
        itemNumber: index + 1,
      }));
      return processedData;
    } else {
      return [];
    }
  },
  setProductData,
});
const useTaxPriceCalc = ref(false); // 控制计算方式

const calculateRow = (row: OrderProductInfo) => {
  row.quantity = row.returnQuantity;
  numCalculateRow(row, useTaxPriceCalc.value, gridApi);
};
const batchSettingConfirm = (data: BatchSettingData) => {
  numBatchSettingConfirm(data, gridApi, productList, useTaxPriceCalc.value);
};
// 批量设置
const batchSetting = () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      batchSettingModal.value.openModal(useTaxPriceCalc.value);
    } else {
      message.warning('请选择数据');
    }
  }
};
// 重置
const resetTableData = () => {
  emit('emitReset', props.propsData.salesOrderId);
};
</script>

<template>
  <div>
    <Grid>
      <template #toolbar-actions>
        <a-checkbox class="mr-2" @change="(e: any) => (useTaxPriceCalc = e.target.checked)">
          按不含税单价计算
        </a-checkbox>
      </template>
      <template #toolbarTools>
        <a-space>
          <a-button class="mr-2" type="primary" @click="exportProductApi(productList, 'sales/return')">
            导出商品
          </a-button>
          <a-button class="mr-2" type="primary" @click="batchSetting">批量设置</a-button>
          <a-button class="mr-2" @click="resetTableData"> 重置 </a-button>
          <a-button class="mr-2" danger @click="removeAccount">删行</a-button>
        </a-space>
      </template>
      <template #edit_productName="{ row }">
        <a-input v-model:value="row.productName" />
      </template>
      <template #edit_productAlias="{ row }">
        <a-input v-model:value="row.productAlias" />
      </template>
      <template #edit_skuName="{ row }">
        <a-input v-model:value="row.specifications" />
      </template>
      <template #edit_spuCode="{ row }">
        <a-input v-model:value="row.productCode" />
      </template>
      <template #edit_measureUnit="{ row }">
        <Input v-model:value="row.measureUnit" />
      </template>
      <template #edit_brandName="{ row }">
        <a-input v-model:value="row.brandName" />
      </template>
      <template #edit_originName="{ row }">
        <a-input v-model:value="row.originName" />
      </template>
      <template #edit_originalQuantity="{ row }">
        <a-input-number v-model:value="row.originalQuantity" />
      </template>
      <template #edit_returnQuantity="{ row }">
        <a-input-number
          v-model:value="row.returnQuantity"
          placeholder="请输入重量"
          @change="calculateRow(row)"
          :precision="4"
        />
      </template>
      <template #edit_priceWithTax="{ row }">
        <a-input-number
          v-model:value="row.priceWithTax"
          placeholder="请输入含税单价"
          @change="calculateRow(row)"
          :disabled="useTaxPriceCalc"
          :precision="2"
        />
      </template>
      <template #edit_taxRate="{ row }">
        <a-input-number
          v-model:value="row.taxRate"
          placeholder="请输入税率"
          @change="calculateRow(row)"
          :precision="4"
        />
      </template>
      <template #edit_amountWithTax="{ row }">
        <a-input-number v-model:value="row.amountWithTax" />
      </template>
      <template #edit_taxAmount="{ row }">
        <a-input-number v-model:value="row.taxAmount" />
      </template>
      <template #edit_amountWithoutTax="{ row }">
        <a-input-number v-model:value="row.amountWithoutTax" />
      </template>
      <template #edit_priceWithoutTax="{ row }">
        <a-input-number
          v-model:value="row.priceWithoutTax"
          placeholder="请输入不含税单价"
          @change="calculateRow(row)"
          :disabled="!useTaxPriceCalc"
          :precision="2"
        />
      </template>
      <template #edit_remarks="{ row }">
        <a-input v-model:value="row.remarks" placeholder="请输入备注" />
      </template>
    </Grid>
    <Modal />
    <BatchSettingModal ref="batchSettingModal" @confirm="batchSettingConfirm" />
  </div>
</template>

<style scoped></style>
