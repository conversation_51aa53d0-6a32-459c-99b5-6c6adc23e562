<script setup lang="ts">
import type { BatchSettingData } from '../components/comm';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { OrderInfo, OrderProductInfo } from '#/api';

import { ref } from 'vue';

import { ImportData } from '@vben/base-ui';
import { useVbenModal } from '@vben/common-ui';
import { getCombinedErrorMessagesString } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { downloadSalesTemplateApi, exportProductApi, importSalesApi } from '#/api';
import { formatFun, plusFun } from '#/utils/calculate';

import BatchSettingModal from '../components/batchSettingModal.vue';
import { baseGridOptions, numBatchSettingConfirm, numCalculateRow } from '../components/comm';
import ProductPopup from '../components/product-popup.vue';

const props = defineProps<{
  formProps: OrderInfo;
}>();
const emit = defineEmits(['orderInfoFun']);
const productList = ref<OrderProductInfo[]>([]);
const batchSettingModal = ref();

// 新增初始化方法
const setProductData = (data: OrderProductInfo[]) => {
  const formattedData = data.map((item) => {
    return {
      ...item,
      amountWithTax: formatFun(item.amountWithTax),
      taxAmount: formatFun(item.taxAmount),
      amountWithoutTax: formatFun(item.amountWithoutTax),
      priceWithTax: formatFun(item.priceWithTax),
      priceWithoutTax: formatFun(item.priceWithoutTax),
    };
  });
  productList.value = formattedData;
  if (gridApi.grid) {
    gridApi.grid.reloadData(productList.value);
  }
};
// 表格配置
const gridOptions = {
  ...baseGridOptions,
  editRules: {
    quantity: [
      { required: true, message: '请输入销售重量' },
      { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入正确重量,最多4位小数' },
    ],
    taxRate: [
      { required: true, message: '请输入税率' },
      { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入正确税率,最多4位小数' },
    ],
    priceWithTax: [
      { required: true, message: '请输入含税单价' },
      { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确含税单价,最多2位小数' },
    ],
    priceWithoutTax: [
      { required: true, message: '请输入不含税单价' },
      { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确不含税单价,最多2位小数' },
    ],
  },
  columns: [
    { field: 'checkbox', type: 'checkbox', width: '50px', fixed: 'left' },
    {
      field: 'categoryName',
      title: '商品分类',
    },
    {
      field: 'productName',
      title: '商品名称',
      slots: { edit: 'edit_productName' },
    },
    {
      field: 'productCode',
      title: '商品编码',
      slots: { edit: 'edit_spuCode' },
    },
    {
      field: 'productAlias',
      title: '商品别名',
      slots: { edit: 'edit_productAlias' },
    },
    {
      field: 'specifications',
      title: '规格型号',
      slots: { edit: 'edit_skuName' },
    },
    {
      field: 'measureUnit',
      title: '计量单位',
      slots: { edit: 'edit_measureUnit' },
    },
    {
      field: 'brandName',
      title: '牌号/材质',
      slots: { edit: 'edit_brandName' },
    },
    {
      field: 'originName',
      title: '产地/厂商',
      slots: { edit: 'edit_originName' },
    },
    {
      field: 'quantity',
      title: '销售重量',
      slots: { default: 'edit_quantity' },
    },
    {
      field: 'priceWithTax',
      title: '含税单价',
      slots: { default: 'edit_priceWithTax' },
    },
    {
      field: 'taxRate',
      title: '税率(%)',
      slots: { default: 'edit_taxRate' },
    },
    {
      field: 'amountWithTax',
      title: '含税金额',
      slots: { edit: 'edit_amountWithTax' },
    },
    {
      field: 'taxAmount',
      title: '税额',
      slots: { edit: 'edit_taxAmount' },
    },
    {
      field: 'amountWithoutTax',
      title: '不含税金额',
      slots: { edit: 'edit_amountWithoutTax' },
    },
    {
      field: 'priceWithoutTax',
      title: '不含税单价',
      slots: { default: 'edit_priceWithoutTax' },
    },
    {
      field: 'itemNumber',
      title: '销售订单行号',
      type: 'seq',
    },
    {
      field: 'remarks',
      title: '备注',
      slots: { default: 'edit_remarks' },
      minWidth: '160px',
    },
  ],
  footerMethod({ $grid }) {
    const data = $grid?.getTableData().visibleData || [];
    let amountWithTax = 0;
    let taxAmount = 0;
    let amountWithoutTax = 0;
    data.forEach((item) => {
      amountWithTax = plusFun(amountWithTax, item.amountWithTax || 0);
      taxAmount = plusFun(taxAmount, item.taxAmount || 0);
      amountWithoutTax = plusFun(amountWithoutTax, item.amountWithoutTax || 0);
    });
    const footerRow = {
      checkbox: '合计',
      amountWithTax: formatFun(amountWithTax),
      taxAmount: formatFun(taxAmount),
      amountWithoutTax: formatFun(amountWithoutTax),
    };
    return [footerRow];
  },
  data: productList.value,
} as VxeTableGridOptions;

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

const [Modal, modalApi] = useVbenModal({
  connectedComponent: ProductPopup,
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      // 弹窗关闭时获取返回的数据
      const selectedProducts = modalApi.getData() || [];
      gridApi.grid.insertAt(selectedProducts, -1);
    }
  },
});
// 删除账户行
const removeAccount = async () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      $grid.removeCheckboxRow();
      message.success('删除成功');
    } else {
      message.warning('请选择数据');
    }
  }
};

// 暴露方法给父组件
defineExpose({
  async getProductData() {
    const $grid = gridApi.grid;
    if ($grid) {
      const errMap = await $grid.validate(true);
      if (errMap) {
        const errMessage = getCombinedErrorMessagesString(errMap);
        if (errMessage) {
          message.error(errMessage);
          return null;
        }
      }
      const { visibleData } = gridApi.grid.getTableData();
      const processedData = visibleData.map((item, index) => ({
        ...item,
        itemNumber: index + 1,
      }));
      // 获取表格footer合计数据
      const footerData: any = $grid.getTableData().footerData?.[0];
      return {
        items: processedData,
        totalAmountWithTax: footerData.amountWithTax,
        totalTaxAmount: footerData.taxAmount,
        totalAmountWithoutTax: footerData.amountWithoutTax,
      };
    } else {
      return [];
    }
  },
  setProductData,
});

const useTaxPriceCalc = ref(false); // 控制计算方式

const calculateRow = (row: OrderProductInfo) => {
  numCalculateRow(row, useTaxPriceCalc.value, gridApi);
};
const batchSettingConfirm = (data: BatchSettingData) => {
  numBatchSettingConfirm(data, gridApi, productList, useTaxPriceCalc.value);
};
const batchSetting = () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      batchSettingModal.value.openModal(useTaxPriceCalc.value); // 打开批量设置弹窗
    } else {
      message.warning('请选择数据');
    }
  }
};
const importSuccess = (data: any) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const newData = data;
    $grid.reloadData(newData);
  }
};
// 重置
const resetTableData = () => {
  emit('orderInfoFun', props.formProps.purchaseOrderId);
};
</script>

<template>
  <div>
    <Grid>
      <template #toolbar-actions>
        <a-checkbox class="mr-2" @change="(e: any) => (useTaxPriceCalc = e.target.checked)">
          按不含税单价计算
        </a-checkbox>
      </template>
      <template #toolbarTools>
        <a-space>
          <ImportData
            v-show="props.formProps.businessStructureType === '先销后采'"
            title="导入商品"
            :upload-api="importSalesApi"
            :download-template-api="downloadSalesTemplateApi"
            @import-success="importSuccess"
          />
          <a-button
            v-show="props.formProps.businessStructureType === '先销后采'"
            class="mr-2"
            type="primary"
            @click="modalApi.setData({}).open()"
          >
            选择商品
          </a-button>
          <a-button
            v-show="props.formProps.businessStructureType === '先采后销'"
            class="mr-2"
            type="primary"
            @click="exportProductApi(productList, 'sales')"
          >
            导出商品
          </a-button>
          <a-button class="mr-2" type="primary" @click="batchSetting">批量设置</a-button>
          <a-button
            v-show="props.formProps.businessStructureType === '先采后销'"
            class="mr-2"
            danger
            @click="resetTableData"
          >
            重置
          </a-button>
          <a-button class="mr-2" danger @click="removeAccount">删行</a-button>
        </a-space>
      </template>
      <template #edit_productName="{ row }">
        <a-input v-model:value="row.productName" placeholder="请输入商品名称" />
      </template>
      <template #edit_productAlias="{ row }">
        <a-input v-model:value="row.productAlias" placeholder="请输入商品别名" />
      </template>
      <template #edit_skuName="{ row }">
        <a-input v-model:value="row.specifications" placeholder="请输入规格型号" />
      </template>
      <template #edit_spuCode="{ row }">
        <a-input v-model:value="row.productCode" placeholder="请输入商品编码" />
      </template>
      <template #edit_measureUnit="{ row }">
        <Input v-model:value="row.measureUnit" placeholder="请输入计量单位" />
      </template>
      <template #edit_brandName="{ row }">
        <a-input v-model:value="row.brandName" placeholder="请输入商品品牌" />
      </template>
      <template #edit_originName="{ row }">
        <a-input v-model:value="row.originName" placeholder="请输入生产厂家" />
      </template>
      <template #edit_quantity="{ row }">
        <a-input-number
          v-model:value="row.quantity"
          placeholder="请输入采购重量"
          @change="calculateRow(row)"
          :precision="4"
        />
      </template>
      <template #edit_priceWithTax="{ row }">
        <a-input-number
          v-model:value="row.priceWithTax"
          placeholder="请输入含税单价"
          @change="calculateRow(row)"
          :disabled="useTaxPriceCalc"
          :precision="2"
        />
      </template>
      <template #edit_taxRate="{ row }">
        <a-input-number
          v-model:value="row.taxRate"
          placeholder="请输入税率"
          @change="calculateRow(row)"
          :precision="4"
        />
      </template>
      <template #edit_amountWithTax="{ row }">
        <a-input-number v-model:value="row.amountWithTax" placeholder="请输入含税金额" />
      </template>
      <template #edit_taxAmount="{ row }">
        <a-input-number v-model:value="row.taxAmount" placeholder="请输入税额" />
      </template>
      <template #edit_amountWithoutTax="{ row }">
        <a-input-number v-model:value="row.amountWithoutTax" placeholder="请输入不含税金额" />
      </template>
      <template #edit_priceWithoutTax="{ row }">
        <a-input-number
          v-model:value="row.priceWithoutTax"
          placeholder="请输入不含税单价"
          @change="calculateRow(row)"
          :disabled="!useTaxPriceCalc"
          :precision="2"
        />
      </template>
      <template #edit_remarks="{ row }">
        <a-input v-model:value="row.remarks" placeholder="请输入备注" />
      </template>
    </Grid>
    <Modal />
    <BatchSettingModal ref="batchSettingModal" @confirm="batchSettingConfirm" />
  </div>
</template>

<style scoped></style>
