<script setup lang="ts">
import type { AccessInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { addAccessApi, editAccessApi, getAccessDetailApi, getCompanyInfoApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import CompanyBaseDetail from '#/views/company/components/company-base-detail.vue';
import CompanyLegalPersonDetail from '#/views/company/components/company-legal-person-detail.vue';

const emit = defineEmits(['ok', 'register']);
const { startInitWorkflow, checkWorkflow, approvalDetail } = useWorkflowBase();
startInitWorkflow({ formKey: 'company_access' });

const companyInfo = ref({});
const init = async (data: AccessInfo) => {
  if (data.companyId) {
    companyInfo.value = await getCompanyInfoApi({ id: data.companyId });
  }
  accessForm.value = data.id
    ? await getAccessDetailApi({ id: data.id })
    : {
        ...data,
      };
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const AccessFormRef = ref();
const accessForm = ref<AccessInfo>({});
const save = async () => {
  await AccessFormRef.value.validate();
  const key = await checkWorkflow();
  return false;
  changeOkLoading(true);
  let api = addAccessApi;
  if (accessForm.value.id) {
    api = editAccessApi;
  }
  try {
    const formData = cloneDeep(accessForm.value);
    const res = await api(accessForm.value as AccessInfo);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const rules = {};
const formProp = {
  colon: false,
  labelCol: { span: 2 },
  wrapperCol: { span: 22 },
};
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="准入信息" ok-text="提交" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <!-- 基本信息 -->
      <CompanyBaseDetail :company-info="companyInfo" />
      <!-- 法人基本信息 -->
      <CompanyLegalPersonDetail :company-info="companyInfo" />
      <BasicCaption content="准入材料" />
      <a-form ref="AccessFormRef" class="mt-5" :model="accessForm" :rules="rules" v-bind="formProp">
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="accessForm.remark" :rows="4" class="w-full" />
        </a-form-item>
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
