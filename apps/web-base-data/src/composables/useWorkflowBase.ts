import type { WorkflowOptions } from '@vben/base-ui';

import { useWorkflow } from '@vben/base-ui';

import { getApprovalDetailApi, getProcessDefinitionApi, getProcessDefinitionListByFormKeyApi } from '#/api';

export function useWorkflowBase(options?: WorkflowOptions) {
  return useWorkflow(
    {
      getProcessDefinitionApi,
      getApprovalDetailApi,
      getProcessDefinitionListByFormKeyApi,
    },
    options,
  );
}
