export default eventHandler((event) => {
  const { parentId } = getQuery(event);
  const tree = [
    {
      id: '1',
      code: 'ABC123',
      name: '大连汇融数科',
      fullName: '大连汇融数科',
      type: 'COMPANY',
      description: '根',
      sortCode: 0,
      managerIds: [],
      isLeaf: 0,
      parentId: '-1',
      parentName: null,
      parentFullName: null,
      parentPath: '1',
      level: 1,
      propertyJson: null,
      createBy: -1,
      createTime: '2025-03-10 11:45:24',
      updateBy: -1,
      updateTime: '2025-03-10 11:45:24',
    },
  ];
  const subList = [
    {
      id: '2',
      code: '1001',
      account: 'account',
      name: '姓名1',
      headIcon: null,
      gender: 'MALE',
      phone: '***********',
      email: '<EMAIL>',
      password: '10470c3b4b1fed12c3baac014be15fac67c6e815',
      expiryDate: null,
      firstLogTime: null,
      firstLogIp: null,
      prevLogTime: null,
      prevLogIp: null,
      lastLogTime: '2025-03-20 18:58:18',
      lastLogIp: null,
      logSuccessCount: 0,
      changePasswordDate: '2025-03-18 11:44:48',
      propertyJson: null,
      source: 'SELF',
      isSupervisor: null,
      managerId: '1',
      organizeIds: ['1', '2', '3'],
      organizeNames: ['大连汇融数科', '大连汇融数科软件部', '大连汇融数科软件部1'],
      organizeFullNames: ['大连汇融数科', '大连汇融数科/大连汇融数科软件部', '大连汇融数科/大连汇融数科软件部1'],
      postIds: [],
      postNames: [],
      roleIds: [],
      roleNames: [],
      groupIds: [],
      groupNames: [],
      isAdmin: 1,
      status: 1,
      createBy: null,
      createTime: '2025-03-15 15:36:45',
      updateBy: null,
      updateTime: '2025-03-18 15:44:23',
      sortCode: 0,
      description: '说明',
      tenantId: '1',
      type: 'user',
      isLeaf: 0,
    },
  ];
  let list = [];
  if (parentId === '-1') {
    list = tree;
  } else if (parentId === '1') {
    list = subList;
  }
  return useResponseSuccess(list);
});
