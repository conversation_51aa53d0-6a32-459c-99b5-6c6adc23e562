<script setup lang="ts">
import type { BpmProcessInstanceApi } from '#/components/src/workflow/type';

import { useVbenModal } from '@vben/common-ui';

import { WorkFlowTimeLine } from '#/components';

defineProps<{
  activityNodes: BpmProcessInstanceApi.ApprovalNodeInfo[];
  api: Object;
}>();
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    globalResolve && globalResolve(true);
    globalResolve = null;
    globalReject = null;
    await modalApi.close();
  },
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      globalReject && globalReject(new Error('取消编辑'));
      globalResolve = null;
      globalReject = null;
    }
  },
});
let globalResolve: ((value: any) => void) | null = null;
let globalReject: ((reason?: unknown) => void) | null = null;
const confirm = async () => {
  return new Promise((resolve, reject) => {
    globalResolve = resolve;
    globalReject = reject;
    modalApi.open();
  });
};
const handleUserSelectConfirm = (activityId: number, userList: any[]) => {
  console.log(activityId, userList);
};
defineExpose({ modalApi, confirm });
</script>

<template>
  <Modal title="工作流节点">
    <WorkFlowTimeLine :activity-nodes="activityNodes" :api="api" @select-user-confirm="handleUserSelectConfirm" />
  </Modal>
</template>

<style></style>
