import type { Ref } from 'vue';

import type { BpmModelApi } from '#/components/src/workflow/type';

import { computed, nextTick, ref } from 'vue';

import { confirm, prompt } from '@vben/common-ui';

import { Select } from 'ant-design-vue';

export interface WorkflowConfig {
  key?: string;
  processInstanceId?: string;
  processDefinitionId?: string;
  formKey?: string;
}

export interface WorkflowOptions {
  operationButtonRef?: Ref<any>;
}

export interface WorkflowApiConfig {
  getProcessDefinitionApi: (params: { id?: string; key?: string }) => Promise<any>;
  getApprovalDetailApi: (params: {
    [key: string]: any;
    processDefinitionId?: string;
    processInstanceId?: string;
  }) => Promise<any>;
  getProcessDefinitionListByFormKeyApi: (params: { formKey: string }) => Promise<any>;
}

export function useWorkflow(apiConfig: WorkflowApiConfig, options?: WorkflowOptions) {
  const config = ref<WorkflowConfig>({});
  const processDefinition = ref<any>({});
  const approvalDetail = ref<any>({});
  const processInstance = ref<any>({});
  const processDefinitionList = ref<BpmModelApi.ProcessDefinition[]>([]);
  const isWorkflow = computed(() => config.value.key && config.value.processInstanceId);
  const getProcessDefinitionDetail = async () => {
    if (!config.value.key) return;
    try {
      processDefinition.value = await apiConfig.getProcessDefinitionApi({ key: config.value.key });
    } catch (error) {
      console.error('获取流程定义失败', error);
    }
  };
  const getApprovalDetail = async () => {
    console.log('config.value===>', config.value);
    if (!config.value.processInstanceId && !config.value.processDefinitionId) return;
    console.log('getApprovalDetail===>', config.value);
    approvalDetail.value = await apiConfig.getApprovalDetailApi({
      processInstanceId: config.value.processInstanceId,
      processDefinitionId: config.value.processDefinitionId,
    });
    processInstance.value = approvalDetail.value.processInstance;
  };
  const getProcessDefinitionList = async () => {
    if (!config.value.formKey) return;
    processDefinitionList.value = await apiConfig.getProcessDefinitionListByFormKeyApi({
      formKey: config.value.formKey,
    });
  };
  const initWorkflow = async (data?: WorkflowConfig) => {
    config.value = Object.assign(config.value, data);
    await getProcessDefinitionDetail();
    await getApprovalDetail();
    if (options?.operationButtonRef) {
      await nextTick();
      options.operationButtonRef.value?.loadTodoTask(approvalDetail.value.todoTask);
    }
  };
  const startInitWorkflow = async (data: { formKey: string }) => {
    config.value.formKey = data.formKey;
    await getProcessDefinitionList();
  };
  const checkWorkflow = async () => {
    let key: string | undefined;
    let processDefinition: BpmModelApi.ProcessDefinition | undefined;
    if (processDefinitionList.value.length === 0) {
      await confirm('未匹配到可用的审批流程，提交将立即生效，是否确认？', '提示');
    } else if (processDefinitionList.value.length === 1 && processDefinitionList.value[0]) {
      key = processDefinitionList.value[0].key;
      processDefinition = processDefinitionList.value[0];
    } else {
      key = await prompt({
        component: Select,
        componentProps: {
          options: processDefinitionList.value,
          placeholder: '请选择',
          popupClassName: 'pointer-events-auto',
          fieldNames: { label: 'name', value: 'key' },
        },
        content: '请选择审批流程',
        title: '',
        modelPropName: 'value',
      });
      processDefinition = processDefinitionList.value.find((item: any) => item.key === key);
      config.value.key = key;
      config.value.processDefinitionId = processDefinition?.id;
      await getApprovalDetail();
    }
    return key;
  };
  return {
    initWorkflow,
    startInitWorkflow,
    checkWorkflow,
    isWorkflow,
    processDefinition,
    processInstance,
    approvalDetail,
  };
}
