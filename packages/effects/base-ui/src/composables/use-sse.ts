import { onUnmounted, ref } from 'vue';

/**
 * 一个可复用的 Vue Composable，用于管理 Server-Sent Events (SSE) 连接。
 * @param {string} url - SSE 端点的 URL
 * @returns {object} 包含 SSE 状态和控制方法的对象
 */
export function useSse(url: string) {
  const status = ref('DISCONNECTED'); // DISCONNECTED, CONNECTING, CONNECTED
  const connectionId = ref<null | string>(null);
  const messages = ref<{ data: any; timestamp: string; type: string }[]>([]);
  const error = ref<null | string>(null);

  let eventSource: EventSource | null = null;

  // ---- 核心方法 ----
  const connect = () => {
    if (eventSource) {
      disconnect();
    }

    // eslint-disable-next-line no-console
    console.log(`[SSE] 正在连接到 ${url}...`);
    status.value = 'CONNECTING';

    eventSource = new EventSource(url);

    eventSource.addEventListener('open', () => {
      status.value = 'CONNECTED';
      error.value = null;
      // eslint-disable-next-line no-console
      console.log('[SSE] 连接已建立。');
      addMessageToLog('system', '连接已打开。');
    });

    eventSource.addEventListener('error', (event) => {
      status.value = 'DISCONNECTED';
      error.value = '连接错误。正在重连...';
      console.error('[SSE] EventSource 失败：', event);
      addMessageToLog('error', '连接丢失。正在重连...');
    });

    eventSource.addEventListener('connected', (event) => {
      const customEvent = event as MessageEvent & { id: null | string };
      connectionId.value = customEvent.id;
      // eslint-disable-next-line no-console
      console.log(`[SSE] 收到 连接成功消息：${customEvent.data}`);
      addMessageToLog(customEvent.type, `收到 连接成功消息：${customEvent.data}`);
    });
    eventSource.addEventListener('message', (event) => {
      handleEvent(event);
    });
    eventSource.addEventListener('login_msg_event', (event) => {
      handleEvent(event);
    });
    eventSource.addEventListener('msg_event', (event) => {
      handleEvent(event);
    });
  };

  const disconnect = () => {
    if (eventSource) {
      eventSource.close();
      eventSource = null;
      status.value = 'DISCONNECTED';
      connectionId.value = null;
      // eslint-disable-next-line no-console
      console.log('[SSE] 连接已手动关闭。');
      addMessageToLog('system', '连接已手动关闭。');
    }
  };

  // ---- 辅助函数 ----
  const handleEvent = (event: MessageEvent) => {
    try {
      const data = JSON.parse(event.data);
      addMessageToLog(event.type, data);
    } catch {
      addMessageToLog(event.type, event.data);
    }
  };

  const addMessageToLog = (type: string, data: any) => {
    messages.value.unshift({
      type,
      data,
      timestamp: new Date().toLocaleTimeString(),
    });
  };

  onUnmounted(() => {
    disconnect();
  });

  return {
    status,
    connectionId,
    messages,
    error,
    connect,
    disconnect,
  };
}
